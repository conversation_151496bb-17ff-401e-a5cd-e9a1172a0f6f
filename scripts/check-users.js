const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function checkUsers() {
  try {
    console.log('🔍 Kontrola uživatelů v databázi...\n')

    const users = await prisma.user.findMany({
      include: {
        organizationMembers: {
          include: {
            organization: true
          }
        }
      }
    })

    if (users.length === 0) {
      console.log('❌ Žádní uživatelé v databázi!')
      return
    }

    console.log(`✅ Nalezeno ${users.length} uživatelů:\n`)

    for (const user of users) {
      console.log(`📧 Email: ${user.email}`)
      console.log(`👤 Jméno: ${user.name || 'Není nastaveno'}`)
      console.log(`🔑 Heslo hash: ${user.password.substring(0, 20)}...`)
      console.log(`📅 Vytvořen: ${user.createdAt}`)
      
      if (user.organizationMembers.length > 0) {
        console.log(`🏢 Organizace:`)
        user.organizationMembers.forEach(member => {
          console.log(`   - ${member.organization.name} (${member.role})`)
        })
      } else {
        console.log(`🏢 Organizace: Žádné`)
      }
      
      console.log('─'.repeat(50))
    }

    // Test přihlášení admin uživatele
    console.log('\n🧪 Test přihlášení <EMAIL>...')
    const adminUser = users.find(u => u.email === '<EMAIL>')
    
    if (adminUser) {
      const isValidPassword = await bcrypt.compare('admin123', adminUser.password)
      console.log(`✅ Heslo admin123 je ${isValidPassword ? 'SPRÁVNÉ' : 'NESPRÁVNÉ'}`)
    } else {
      console.log('❌ Admin uživatel nenalezen!')
    }

  } catch (error) {
    console.error('❌ Chyba při kontrole uživatelů:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkUsers()
