const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function fixAdminPassword() {
  try {
    console.log('🔧 Opravuji <NAME_EMAIL>...')

    // Vytvoření nového hash hesla
    const newPassword = 'admin123'
    const hashedPassword = await bcrypt.hash(newPassword, 12)

    // Aktualizace hesla v databázi
    const updatedUser = await prisma.user.update({
      where: { email: '<EMAIL>' },
      data: { password: hashedPassword }
    })

    console.log('✅ Heslo bylo úspěšně aktualizováno!')

    // Test nového hesla
    const isValid = await bcrypt.compare(newPassword, hashedPassword)
    console.log(`🧪 Test hesla: ${isValid ? 'ÚSPĚŠNÝ' : 'NEÚSPĚŠNÝ'}`)

    console.log('\n📋 Aktualizované přihlašovací údaje:')
    console.log('┌─────────────────────────────────────────┐')
    console.log('│ ADMIN:                                  │')
    console.log('│ Email: <EMAIL>                    │')
    console.log('│ Heslo: admin123                         │')
    console.log('└─────────────────────────────────────────┘')

  } catch (error) {
    console.error('❌ Chyba při opravě hesla:', error)
  } finally {
    await prisma.$disconnect()
  }
}

fixAdminPassword()
