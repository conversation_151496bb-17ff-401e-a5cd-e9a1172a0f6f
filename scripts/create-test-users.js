const { PrismaClient } = require('@prisma/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

async function createTestUsers() {
  try {
    console.log('🔄 Vytváření testovacích uživatelů...')

    // Hash hesla
    const adminPassword = await bcrypt.hash('admin123', 12)
    const technikPassword = await bcrypt.hash('technik123', 12)
    const ctenarPassword = await bcrypt.hash('ctenar123', 12)

    // Vytvoření organizace
    const organization = await prisma.organization.upsert({
      where: { slug: 'test-organizace' },
      update: {},
      create: {
        name: 'Test Organizace',
        slug: 'test-organizace',
        description: 'Testovací organizace pro vývoj'
      }
    })

    console.log('✅ Organizace vytvořena:', organization.name)

    // Vytvoření admin uživatele
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: adminPassword,
        name: 'Ad<PERSON> Testovací'
      }
    })

    // Přidání admin uživatele do organizace
    await prisma.organizationMember.upsert({
      where: {
        userId_organizationId: {
          userId: adminUser.id,
          organizationId: organization.id
        }
      },
      update: {},
      create: {
        userId: adminUser.id,
        organizationId: organization.id,
        role: 'admin'
      }
    })

    console.log('✅ Admin uživatel vytvořen: <EMAIL> / admin123')

    // Vytvoření technik uživatele
    const technikUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: technikPassword,
        name: 'Technik Testovací'
      }
    })

    await prisma.organizationMember.upsert({
      where: {
        userId_organizationId: {
          userId: technikUser.id,
          organizationId: organization.id
        }
      },
      update: {},
      create: {
        userId: technikUser.id,
        organizationId: organization.id,
        role: 'technik'
      }
    })

    console.log('✅ Technik uživatel vytvořen: <EMAIL> / technik123')

    // Vytvoření čtenář uživatele
    const ctenarUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        password: ctenarPassword,
        name: 'Čtenář Testovací'
      }
    })

    await prisma.organizationMember.upsert({
      where: {
        userId_organizationId: {
          userId: ctenarUser.id,
          organizationId: organization.id
        }
      },
      update: {},
      create: {
        userId: ctenarUser.id,
        organizationId: organization.id,
        role: 'ctenar'
      }
    })

    console.log('✅ Čtenář uživatel vytvořen: <EMAIL> / ctenar123')

    // Vytvoření několik testovacích zařízení
    const devices = [
      {
        evidenceNumber: 'PC001',
        name: 'Dell OptiPlex 7090',
        description: 'Pracovní počítač pro kancelář',
        deviceType: 'Počítač',
        manufacturer: 'Dell',
        model: 'OptiPlex 7090',
        serialNumber: 'DL123456789',
        location: 'Kancelář 101',
        status: 'aktivni'
      },
      {
        evidenceNumber: 'MON001',
        name: 'Samsung 24" Monitor',
        description: 'LCD monitor pro pracovní stanici',
        deviceType: 'Monitor',
        manufacturer: 'Samsung',
        model: 'S24F350',
        serialNumber: 'SM987654321',
        location: 'Kancelář 101',
        status: 'aktivni'
      },
      {
        evidenceNumber: 'LAP001',
        name: 'Lenovo ThinkPad X1',
        description: 'Služební notebook',
        deviceType: 'Notebook',
        manufacturer: 'Lenovo',
        model: 'ThinkPad X1 Carbon',
        serialNumber: 'LN456789123',
        location: 'Mobilní',
        status: 'zapujceno'
      }
    ]

    for (const deviceData of devices) {
      await prisma.device.upsert({
        where: { evidenceNumber: deviceData.evidenceNumber },
        update: {},
        create: {
          ...deviceData,
          organizationId: organization.id
        }
      })
    }

    console.log('✅ Testovací zařízení vytvořena')

    console.log('\n🎉 Testovací data úspěšně vytvořena!')
    console.log('\n📋 Přihlašovací údaje:')
    console.log('┌─────────────────────────────────────────┐')
    console.log('│ ADMIN:                                  │')
    console.log('│ Email: <EMAIL>                    │')
    console.log('│ Heslo: admin123                         │')
    console.log('├─────────────────────────────────────────┤')
    console.log('│ TECHNIK:                                │')
    console.log('│ Email: <EMAIL>                  │')
    console.log('│ Heslo: technik123                       │')
    console.log('├─────────────────────────────────────────┤')
    console.log('│ ČTENÁŘ:                                 │')
    console.log('│ Email: <EMAIL>                   │')
    console.log('│ Heslo: ctenar123                        │')
    console.log('└─────────────────────────────────────────┘')

  } catch (error) {
    console.error('❌ Chyba při vytváření testovacích dat:', error)
  } finally {
    await prisma.$disconnect()
  }
}

createTestUsers()
