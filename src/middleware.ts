import { NextResponse, type NextRequest } from 'next/server'
import { getCurrentUser } from '@/lib/auth'

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for API routes, static files, and auth pages
  if (
    pathname.startsWith('/api/') ||
    pathname.startsWith('/_next/') ||
    pathname.startsWith('/auth/') ||
    pathname.includes('.') ||
    pathname === '/favicon.ico'
  ) {
    return NextResponse.next()
  }

  const sessionToken = request.cookies.get('session')?.value

  // If user is not signed in and trying to access protected routes, redirect to login
  if (!sessionToken && pathname.startsWith('/dashboard')) {
    return NextResponse.redirect(new URL('/auth/login', request.url))
  }

  // Kontrola org parametru pro non-superadmin uživatele
  if (pathname.startsWith('/dashboard') && request.nextUrl.searchParams.has('org')) {
    try {
      const user = await getCurrentUser()

      // Pokud není superadmin, odstraň org parametr z URL
      if (user && !user.isSuperAdmin) {
        const url = request.nextUrl.clone()
        url.searchParams.delete('org')
        return NextResponse.redirect(url)
      }
    } catch (error) {
      console.error('Middleware org check error:', error)
      // V případě chyby pokračuj normálně
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - files with extensions
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\..*).*)',
  ],
}
