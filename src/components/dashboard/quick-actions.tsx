'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Plus, 
  Search, 
  QrCode, 
  Download, 
  Upload, 
  Settings, 
  Users, 
  Building2,
  Zap,
  BarChart3,
  FileText,
  Wrench,
  Archive
} from 'lucide-react'
import Link from 'next/link'

interface QuickActionsProps {
  userRole: string
  isSuperAdmin: boolean
  stats?: {
    totalDevices: number
    activeDevices: number
    borrowedDevices: number
    serviceDevices: number
  }
}

export function QuickActions({ userRole, isSuperAdmin, stats }: QuickActionsProps) {
  const router = useRouter()
  const [isExporting, setIsExporting] = useState(false)

  const handleExport = async () => {
    setIsExporting(true)
    try {
      const response = await fetch('/api/devices/export')
      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `devices-export-${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }
    } catch (error) {
      console.error('Export failed:', error)
    } finally {
      setIsExporting(false)
    }
  }

  const primaryActions = [
    {
      title: 'Přidat zařízení',
      description: 'Rychle přidat nové zařízení',
      icon: Plus,
      href: '/dashboard/devices/new',
      color: 'bg-orange-500 hover:bg-orange-600',
      textColor: 'text-white',
      show: ['admin', 'technik'].includes(userRole) || isSuperAdmin
    },
    {
      title: 'Vyhledat zařízení',
      description: 'Najít konkrétní zařízení',
      icon: Search,
      href: '/dashboard/devices',
      color: 'bg-blue-500 hover:bg-blue-600',
      textColor: 'text-white',
      show: true
    },
    {
      title: 'Skenovat QR kód',
      description: 'Rychlý přístup k zařízení',
      icon: QrCode,
      onClick: () => {
        // TODO: Implement QR scanner
        alert('QR skener bude implementován v další verzi')
      },
      color: 'bg-green-500 hover:bg-green-600',
      textColor: 'text-white',
      show: true
    }
  ]

  const secondaryActions = [
    {
      title: 'Exportovat data',
      description: 'Stáhnout seznam zařízení',
      icon: Download,
      onClick: handleExport,
      loading: isExporting,
      show: ['admin'].includes(userRole) || isSuperAdmin
    },
    {
      title: 'Importovat data',
      description: 'Nahrát zařízení z CSV',
      icon: Upload,
      href: '/dashboard/import',
      show: ['admin'].includes(userRole) || isSuperAdmin
    },
    {
      title: 'Správa uživatelů',
      description: 'Spravovat uživatele',
      icon: Users,
      href: '/dashboard/users',
      show: userRole === 'admin' || isSuperAdmin
    },
    {
      title: 'Organizace',
      description: 'Spravovat organizace',
      icon: Building2,
      href: '/dashboard/organizations',
      show: isSuperAdmin
    },
    {
      title: 'Nastavení',
      description: 'Konfigurace systému',
      icon: Settings,
      href: '/dashboard/settings',
      show: userRole === 'admin' || isSuperAdmin
    },
    {
      title: 'Reporty',
      description: 'Generovat reporty',
      icon: BarChart3,
      href: '/dashboard/reports',
      show: ['admin'].includes(userRole) || isSuperAdmin
    }
  ]

  const statusActions = stats ? [
    {
      title: 'Zařízení v servisu',
      count: stats.serviceDevices,
      description: 'Vyžadují pozornost',
      icon: Wrench,
      href: '/dashboard/devices?status=servis',
      color: 'bg-yellow-100 border-yellow-200 hover:bg-yellow-200',
      textColor: 'text-yellow-800',
      show: stats.serviceDevices > 0
    },
    {
      title: 'Zapůjčená zařízení',
      count: stats.borrowedDevices,
      description: 'Momentálně vypůjčená',
      icon: FileText,
      href: '/dashboard/devices?status=zapujceno',
      color: 'bg-blue-100 border-blue-200 hover:bg-blue-200',
      textColor: 'text-blue-800',
      show: stats.borrowedDevices > 0
    }
  ] : []

  return (
    <div className="space-y-6">
      {/* Primary Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Zap className="h-5 w-5 mr-2" />
            Rychlé akce
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {primaryActions.filter(action => action.show).map((action, index) => {
              const IconComponent = action.icon
              const content = (
                <div className={`p-4 rounded-lg ${action.color} ${action.textColor} transition-all duration-200 transform hover:scale-105 cursor-pointer`}>
                  <div className="flex items-center space-x-3">
                    <IconComponent className="h-6 w-6" />
                    <div>
                      <h3 className="font-semibold">{action.title}</h3>
                      <p className="text-sm opacity-90">{action.description}</p>
                    </div>
                  </div>
                </div>
              )

              if (action.href) {
                return (
                  <Link key={index} href={action.href}>
                    {content}
                  </Link>
                )
              }

              return (
                <div key={index} onClick={action.onClick}>
                  {content}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Status-based Actions */}
      {statusActions.some(action => action.show) && (
        <Card>
          <CardHeader>
            <CardTitle>Vyžaduje pozornost</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {statusActions.filter(action => action.show).map((action, index) => {
                const IconComponent = action.icon
                return (
                  <Link key={index} href={action.href}>
                    <div className={`p-4 rounded-lg border-2 ${action.color} ${action.textColor} transition-all duration-200 hover:shadow-md cursor-pointer`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <IconComponent className="h-5 w-5" />
                          <div>
                            <h3 className="font-semibold">{action.title}</h3>
                            <p className="text-sm opacity-75">{action.description}</p>
                          </div>
                        </div>
                        <Badge variant="secondary" className="text-lg font-bold">
                          {action.count}
                        </Badge>
                      </div>
                    </div>
                  </Link>
                )
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Secondary Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Správa systému</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3">
            {secondaryActions.filter(action => action.show).map((action, index) => {
              const IconComponent = action.icon
              const content = (
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2 hover:bg-gray-50"
                  disabled={action.loading}
                >
                  <IconComponent className="h-5 w-5" />
                  <span className="text-xs text-center">{action.title}</span>
                </Button>
              )

              if (action.href) {
                return (
                  <Link key={index} href={action.href}>
                    {content}
                  </Link>
                )
              }

              return (
                <div key={index} onClick={action.onClick}>
                  {content}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
