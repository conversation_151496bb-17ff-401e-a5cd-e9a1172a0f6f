'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Monitor, AlertTriangle, Wrench, Archive, TrendingUp, Users, Building2 } from 'lucide-react'

interface StatsOverview {
  totalDevices: number
  activeDevices: number
  borrowedDevices: number
  serviceDevices: number
  retiredDevices: number
  totalUsers: number
  totalOrganizations: number
}

interface StatsCardsProps {
  stats: StatsOverview
}

export function StatsCards({ stats }: StatsCardsProps) {
  const statusCards = [
    {
      title: '<PERSON><PERSON><PERSON>',
      value: stats.totalDevices,
      icon: Monitor,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
    },
    {
      title: 'Aktivní',
      value: stats.activeDevices,
      icon: Monitor,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
    },
    {
      title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
      value: stats.borrowedDevices,
      icon: TrendingUp,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
    },
    {
      title: 'V servisu',
      value: stats.serviceDevices,
      icon: Wrench,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
    },
    {
      title: 'Vyřazeno',
      value: stats.retiredDevices,
      icon: Archive,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
    },
    {
      title: 'Uživatelé',
      value: stats.totalUsers,
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
    },
    {
      title: 'Organizace',
      value: stats.totalOrganizations,
      icon: Building2,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      borderColor: 'border-indigo-200',
    },
  ]

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-7 gap-4">
      {statusCards.map((card) => (
        <Card key={card.title} className={`border-2 ${card.borderColor} hover:shadow-lg transition-all duration-200`}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium text-gray-700">{card.title}</CardTitle>
            <div className={`p-2 rounded-lg ${card.bgColor}`}>
              <card.icon className={`h-4 w-4 ${card.color}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className={`text-xl sm:text-2xl font-bold ${card.color}`}>{card.value}</div>
            {card.title === 'Celkem zařízení' && stats.totalDevices > 0 && (
              <p className="text-xs text-gray-500 mt-1">
                Celkový počet
              </p>
            )}
            {card.title === 'Aktivní' && stats.totalDevices > 0 && (
              <p className="text-xs text-gray-500 mt-1">
                {Math.round((stats.activeDevices / stats.totalDevices) * 100)}% z celku
              </p>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )

}
