'use client'

import { useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Check, ChevronsUpDown, Building2 } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'

type OrganizationMember = {
  id: string
  role: string
  organization: {
    id: string
    name: string
    slug: string
  }
}

interface OrganizationSwitcherProps {
  organizations: OrganizationMember[]
  isSuperAdmin?: boolean
  allOrganizations?: Array<{
    id: string
    name: string
    slug: string
  }>
}

export function OrganizationSwitcher({ organizations, isSuperAdmin, allOrganizations }: OrganizationSwitcherProps) {
  const [open, setOpen] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // Pro superadmin použij allOrganizations, pro ostatní jejich organizace
  const availableOrgs = isSuperAdmin && allOrganizations
    ? allOrganizations
    : organizations.map(org => org.organization)

  const currentOrgId = searchParams.get('org') || availableOrgs[0]?.id
  const currentOrg = availableOrgs.find(org => org.id === currentOrgId)

  const handleOrgChange = (orgId: string) => {
    // Pouze superadmin může přepínat organizace
    if (!isSuperAdmin) return

    const url = new URL(window.location.href)
    url.searchParams.set('org', orgId)
    router.push(url.pathname + url.search)
    setOpen(false)
  }

  // Pro non-superadmin uživatele zobrazit pouze název organizace bez možnosti přepínání
  if (!isSuperAdmin) {
    if (availableOrgs.length === 0) {
      return (
        <div className="flex items-center space-x-2 p-2 text-sm text-gray-500">
          <Building2 className="h-4 w-4" />
          <span>Žádné organizace</span>
        </div>
      )
    }
    const userOrg = organizations[0]?.organization
    return (
      <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-xl border border-orange-200">
        <div className="p-1.5 bg-orange-100 rounded-lg">
          <Building2 className="h-4 w-4 text-orange-600" />
        </div>
        <div>
          <div className="font-medium text-gray-900">{userOrg?.name}</div>
          <div className="text-xs text-gray-500 capitalize">
            {organizations[0]?.role}
          </div>
        </div>
      </div>
    )
  }

  // Pouze superadmin má přístup k organization switcheru
  return (
    <div className="relative">
      <Button
        variant="outline"
        role="combobox"
        aria-expanded={open}
        className="w-full justify-between h-12 border-orange-200 hover:bg-orange-50 hover:border-orange-300"
        onClick={() => setOpen(!open)}
      >
        <div className="flex items-center space-x-3">
          <div className="p-1.5 bg-orange-100 rounded-lg">
            <Building2 className="h-4 w-4 text-orange-600" />
          </div>
          <div>
            <div className="truncate font-medium">
              {currentOrg?.name || 'Vyberte organizaci'}
            </div>
            {isSuperAdmin && (
              <div className="text-xs text-orange-600 font-medium">SuperAdmin</div>
            )}
          </div>
        </div>
        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
      </Button>
      
      {open && (
        <div className="absolute top-full left-0 right-0 z-50 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg">
          <div className="py-2">
            {availableOrgs.map((org) => (
              <button
                key={org.id}
                className={cn(
                  "relative flex w-full items-center space-x-3 px-4 py-3 text-sm hover:bg-orange-50 transition-colors duration-200",
                  currentOrgId === org.id && "bg-orange-50 text-orange-700"
                )}
                onClick={() => handleOrgChange(org.id)}
              >
                <div className="p-1 bg-orange-100 rounded-lg">
                  <Building2 className="h-4 w-4 text-orange-600" />
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium truncate">{org.name}</div>
                  {!isSuperAdmin && (
                    <div className="text-xs text-gray-500 capitalize">
                      {organizations.find(o => o.organization.id === org.id)?.role}
                    </div>
                  )}
                </div>
                {currentOrgId === org.id && (
                  <Check className="h-4 w-4 text-orange-600" />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
