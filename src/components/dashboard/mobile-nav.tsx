'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  Menu,
  X,
  LayoutDashboard,
  Monitor,
  Building2,
  Users,
  Settings,
  LogOut,
  Check,
  ChevronsUpDown,
  Shield,
  UserCog,
  FileText,
  UsersIcon,
  Building
} from 'lucide-react'


const getNavigation = (isSuperAdmin: boolean) => [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/dashboard/devices', icon: Monitor },
  { name: 'Organizace', href: '/dashboard/organizations', icon: Building2 },
  { name: 'Uživatelé', href: '/dashboard/users', icon: Users },
  { name: 'Role', href: '/dashboard/roles', icon: UserCog },
  { name: 'Tý<PERSON>', href: '/dashboard/teams', icon: UsersIcon },
  { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/dashboard/departments', icon: Building },
  { name: 'Audit Log', href: '/dashboard/audit', icon: FileText },
  ...(isSuperAdmin ? [{ name: 'Správa organizací', href: '/dashboard/admin/organizations', icon: Shield }] : []),
  { name: 'Nastavení', href: '/dashboard/settings', icon: Settings },
]

type OrganizationMember = {
  id: string
  role: string
  organization: {
    id: string
    name: string
    slug: string
  }
}

interface MobileNavProps {
  organizations: OrganizationMember[]
  isSuperAdmin?: boolean
  allOrganizations?: Array<{ id: string; name: string; slug: string }> | null
}

export function MobileNav({ organizations, isSuperAdmin = false, allOrganizations }: MobileNavProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [showOrgSwitcher, setShowOrgSwitcher] = useState(false)
  const pathname = usePathname()
  const router = useRouter()
  const searchParams = useSearchParams()
  const navigation = getNavigation(isSuperAdmin)

  const availableOrgs = isSuperAdmin && allOrganizations ? allOrganizations : organizations.map(org => org.organization)
  const currentOrgId = searchParams.get('org') || availableOrgs[0]?.id
  const currentOrg = availableOrgs.find(org => org.id === currentOrgId)

  const handleLogout = async () => {
    await fetch('/api/auth/logout', { method: 'POST' })
    router.push('/auth/login')
    router.refresh()
  }

  const handleOrgChange = (orgId: string) => {
    // Pouze superadmin může přepínat organizace
    if (!isSuperAdmin) return

    const url = new URL(window.location.href)
    url.searchParams.set('org', orgId)
    router.push(url.pathname + url.search)
    setShowOrgSwitcher(false)
    setIsOpen(false)
  }

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(true)}
        className="md:hidden p-3 hover:bg-orange-50 rounded-xl touch-manipulation active:scale-95 transition-transform"
        aria-label="Otevřít menu"
      >
        <Menu className="h-6 w-6 text-gray-600" />
      </Button>

      {/* Mobile menu overlay */}
      {isOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          <div
            className="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm"
            onClick={() => setIsOpen(false)}
            aria-hidden="true"
          />

          <div className="fixed top-0 right-0 bottom-0 w-full max-w-sm bg-white shadow-2xl rounded-l-2xl transform transition-transform duration-300 ease-out">
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-100 bg-gradient-to-r from-orange-50 to-orange-100">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-orange-100 rounded-xl">
                    <div className="w-6 h-6 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg"></div>
                  </div>
                  <h2 className="text-xl font-bold text-gray-900">RokitBox</h2>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                  className="hover:bg-orange-200 rounded-xl p-3 touch-manipulation active:scale-95 transition-transform"
                  aria-label="Zavřít menu"
                >
                  <X className="h-6 w-6 text-gray-600" />
                </Button>
              </div>

              {/* Organization Switcher */}
              {organizations.length > 0 && (
                <div className="p-4 border-b">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Organizace</label>
                    {!isSuperAdmin ? (
                      <div className="flex items-center space-x-3 p-3 bg-orange-50 rounded-xl border border-orange-200">
                        <div className="p-1.5 bg-orange-100 rounded-lg">
                          <Building2 className="h-4 w-4 text-orange-600" />
                        </div>
                        <div>
                          <div className="font-medium">{currentOrg?.name}</div>
                          <div className="text-xs text-gray-500 capitalize">
                            {organizations.find(org => org.organization.id === currentOrgId)?.role}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="relative">
                        <Button
                          variant="outline"
                          className="w-full justify-between h-12 border-orange-200 hover:bg-orange-50 hover:border-orange-300"
                          onClick={() => setShowOrgSwitcher(!showOrgSwitcher)}
                        >
                          <div className="flex items-center space-x-3">
                            <div className="p-1.5 bg-orange-100 rounded-lg">
                              <Building2 className="h-4 w-4 text-orange-600" />
                            </div>
                            <div>
                              <div className="truncate font-medium">
                                {currentOrg?.name || 'Vyberte organizaci'}
                              </div>
                              {isSuperAdmin && (
                                <div className="text-xs text-orange-600 font-medium">SuperAdmin</div>
                              )}
                            </div>
                          </div>
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                        
                        {showOrgSwitcher && (
                          <div className="absolute top-full left-0 right-0 z-10 mt-2 bg-white border border-gray-200 rounded-xl shadow-lg">
                            <div className="py-2">
                              {availableOrgs.map((org) => (
                                <button
                                  key={org.id}
                                  className={cn(
                                    "relative flex w-full items-center space-x-3 px-4 py-3 text-sm hover:bg-orange-50 transition-colors duration-200",
                                    currentOrgId === org.id && "bg-orange-50 text-orange-700"
                                  )}
                                  onClick={() => handleOrgChange(org.id)}
                                >
                                  <div className="p-1 bg-orange-100 rounded-lg">
                                    <Building2 className="h-4 w-4 text-orange-600" />
                                  </div>
                                  <div className="flex-1 text-left">
                                    <div className="font-medium truncate">{org.name}</div>
                                    {!isSuperAdmin && (
                                      <div className="text-xs text-gray-500 capitalize">
                                        {organizations.find(o => o.organization.id === org.id)?.role}
                                      </div>
                                    )}
                                  </div>
                                  {currentOrgId === org.id && (
                                    <Check className="h-4 w-4 text-orange-600" />
                                  )}
                                </button>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Navigation */}
              <nav className="flex-1 px-6 py-6 space-y-2">
                {navigation.map((item) => {
                  const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      onClick={() => setIsOpen(false)}
                      className={cn(
                        'group flex items-center px-4 py-4 text-base font-medium rounded-xl transition-all duration-200 touch-manipulation active:scale-95',
                        isActive
                          ? 'bg-orange-100 text-orange-900 shadow-sm border border-orange-200'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900 active:bg-gray-100'
                      )}
                    >
                      <item.icon
                        className={cn(
                          'mr-4 h-6 w-6',
                          isActive ? 'text-orange-600' : 'text-gray-400 group-hover:text-gray-500'
                        )}
                      />
                      {item.name}
                    </Link>
                  )
                })}
              </nav>

              {/* Logout */}
              <div className="p-6 border-t border-gray-100 bg-gray-50">
                <button
                  onClick={handleLogout}
                  className="group flex items-center w-full px-4 py-4 text-base font-medium text-gray-600 rounded-xl hover:bg-red-50 hover:text-red-700 transition-all duration-200 touch-manipulation active:scale-95 active:bg-red-100"
                >
                  <LogOut className="mr-4 h-6 w-6 text-gray-400 group-hover:text-red-500" />
                  Odhlásit se
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
