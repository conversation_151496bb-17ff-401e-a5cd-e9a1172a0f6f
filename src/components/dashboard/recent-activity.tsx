'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Monitor, Plus, Edit, Trash2, User, Building2, Clock } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'
import { cs } from 'date-fns/locale'

interface Activity {
  id: string
  type: 'device_created' | 'device_updated' | 'device_deleted' | 'user_created' | 'organization_created'
  title: string
  description: string
  createdAt: string
  user?: {
    name: string
    email: string
  }
  metadata?: {
    deviceName?: string
    deviceType?: string
    organizationName?: string
    status?: string
  }
}

interface RecentActivityProps {
  activities: Activity[]
}

export function RecentActivity({ activities }: RecentActivityProps) {
  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'device_created':
        return <Plus className="h-4 w-4 text-green-600" />
      case 'device_updated':
        return <Edit className="h-4 w-4 text-blue-600" />
      case 'device_deleted':
        return <Trash2 className="h-4 w-4 text-red-600" />
      case 'user_created':
        return <User className="h-4 w-4 text-purple-600" />
      case 'organization_created':
        return <Building2 className="h-4 w-4 text-indigo-600" />
      default:
        return <Monitor className="h-4 w-4 text-gray-600" />
    }
  }

  const getActivityColor = (type: Activity['type']) => {
    switch (type) {
      case 'device_created':
        return 'bg-green-100 border-green-200'
      case 'device_updated':
        return 'bg-blue-100 border-blue-200'
      case 'device_deleted':
        return 'bg-red-100 border-red-200'
      case 'user_created':
        return 'bg-purple-100 border-purple-200'
      case 'organization_created':
        return 'bg-indigo-100 border-indigo-200'
      default:
        return 'bg-gray-100 border-gray-200'
    }
  }

  const getStatusBadge = (status?: string) => {
    if (!status) return null
    
    const statusConfig = {
      aktivni: { label: 'Aktivní', variant: 'default' as const },
      zapujceno: { label: 'Zapůjčeno', variant: 'secondary' as const },
      servis: { label: 'Servis', variant: 'destructive' as const },
      vyrazeno: { label: 'Vyřazeno', variant: 'outline' as const }
    }

    const config = statusConfig[status as keyof typeof statusConfig]
    if (!config) return null

    return <Badge variant={config.variant} className="text-xs">{config.label}</Badge>
  }

  if (activities.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="h-5 w-5 mr-2" />
            Poslední aktivity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Zatím žádné aktivity</p>
            <p className="text-sm text-gray-400 mt-1">
              Aktivity se zobrazí po provedení změn v systému
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="h-5 w-5 mr-2" />
          Poslední aktivity
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div
              key={activity.id}
              className={`p-4 rounded-lg border ${getActivityColor(activity.type)}`}
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  {getActivityIcon(activity.type)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900 truncate">
                      {activity.title}
                    </h4>
                    <div className="flex items-center space-x-2">
                      {getStatusBadge(activity.metadata?.status)}
                      <span className="text-xs text-gray-500">
                        {formatDistanceToNow(new Date(activity.createdAt), {
                          addSuffix: true,
                          locale: cs
                        })}
                      </span>
                    </div>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    {activity.description}
                  </p>
                  {activity.metadata && (
                    <div className="mt-2 flex flex-wrap gap-2 text-xs text-gray-500">
                      {activity.metadata.deviceName && (
                        <span className="bg-white px-2 py-1 rounded border">
                          📱 {activity.metadata.deviceName}
                        </span>
                      )}
                      {activity.metadata.deviceType && (
                        <span className="bg-white px-2 py-1 rounded border">
                          🔧 {activity.metadata.deviceType}
                        </span>
                      )}
                      {activity.metadata.organizationName && (
                        <span className="bg-white px-2 py-1 rounded border">
                          🏢 {activity.metadata.organizationName}
                        </span>
                      )}
                    </div>
                  )}
                  {activity.user && (
                    <div className="mt-2 text-xs text-gray-500">
                      👤 {activity.user.name} ({activity.user.email})
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
