import { lazy } from 'react'
import { ComponentLoadingSpinner } from '@/components/ui/loading-spinner'

// Lazy load heavy components with loading fallback
export const LazyDeviceForm = lazy(() => 
  import('@/components/devices/device-form').then(module => ({
    default: module.DeviceForm
  }))
)

export const LazyFileUpload = lazy(() => 
  import('@/components/devices/file-upload').then(module => ({
    default: module.FileUpload
  }))
)

export const LazyQRCodeGenerator = lazy(() => 
  import('@/components/devices/qr-code-generator').then(module => ({
    default: module.QRCodeGenerator
  }))
)

export const LazyStatsCards = lazy(() => 
  import('@/components/dashboard/stats-cards').then(module => ({
    default: module.StatsCards
  }))
)

export const LazyDeviceHistory = lazy(() => 
  import('@/components/devices/device-history').then(module => ({
    default: module.DeviceHistory
  }))
)

export const LazyUserManagement = lazy(() => 
  import('@/components/admin/user-management').then(module => ({
    default: module.UserManagement
  }))
)

export const LazyOrganizationManagement = lazy(() => 
  import('@/components/admin/organization-management').then(module => ({
    default: module.OrganizationManagement
  }))
)

// HOC for adding loading fallback
export function withLazyLoading<T extends object>(
  Component: React.LazyExoticComponent<React.ComponentType<T>>,
  fallback?: React.ComponentType
) {
  const FallbackComponent = fallback || ComponentLoadingSpinner
  
  return function LazyComponent(props: T) {
    return (
      <React.Suspense fallback={<FallbackComponent />}>
        <Component {...props} />
      </React.Suspense>
    )
  }
}

// Preload functions for better UX
export const preloadComponents = {
  deviceForm: () => import('@/components/devices/device-form'),
  fileUpload: () => import('@/components/devices/file-upload'),
  qrCode: () => import('@/components/devices/qr-code-generator'),
  statsCards: () => import('@/components/dashboard/stats-cards'),
  deviceHistory: () => import('@/components/devices/device-history'),
  userManagement: () => import('@/components/admin/user-management'),
  organizationManagement: () => import('@/components/admin/organization-management'),
}

// Preload on hover/focus for better perceived performance
export function usePreloadOnHover() {
  return {
    onMouseEnter: (componentName: keyof typeof preloadComponents) => {
      preloadComponents[componentName]()
    },
    onFocus: (componentName: keyof typeof preloadComponents) => {
      preloadComponents[componentName]()
    }
  }
}
