'use client'

import { useState, useEffect } from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Keyboard, X } from 'lucide-react'
import { getAllShortcuts, formatShortcut } from '@/hooks/use-keyboard-shortcuts'
import { Button } from '@/components/ui/button'

export function KeyboardShortcutsModal() {
  const [isOpen, setIsOpen] = useState(false)
  const [shortcuts, setShortcuts] = useState<Record<string, any[]>>({})

  useEffect(() => {
    const handleShowShortcuts = () => {
      setIsOpen(true)
      setShortcuts(getAllShortcuts())
    }

    const handleCloseModals = () => {
      setIsOpen(false)
    }

    window.addEventListener('show-shortcuts-modal', handleShowShortcuts)
    window.addEventListener('close-modals', handleCloseModals)

    return () => {
      window.removeEventListener('show-shortcuts-modal', handleShowShortcuts)
      window.removeEventListener('close-modals', handleCloseModals)
    }
  }, [])

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Keyboard className="h-5 w-5 text-orange-600" />
            Klávesové zkratky
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {Object.entries(shortcuts).map(([category, categoryShortcuts]) => (
            <div key={category} className="space-y-3">
              <h3 className="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">
                {category}
              </h3>
              <div className="grid gap-2">
                {categoryShortcuts.map((shortcut, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <span className="text-sm text-gray-700">
                      {shortcut.description}
                    </span>
                    <Badge variant="outline" className="font-mono text-xs">
                      {formatShortcut(shortcut)}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          ))}

          {Object.keys(shortcuts).length === 0 && (
            <div className="text-center py-8">
              <Keyboard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Žádné klávesové zkratky nejsou k dispozici</p>
            </div>
          )}
        </div>

        <div className="flex justify-end pt-4 border-t border-gray-200">
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            className="flex items-center gap-2"
          >
            <X className="h-4 w-4" />
            Zavřít
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
