'use client'

import * as React from "react"
import { cn } from "@/lib/utils"
import { RefreshCw } from "lucide-react"

interface PullToRefreshProps {
  children: React.ReactNode
  onRefresh: () => Promise<void> | void
  className?: string
  disabled?: boolean
  threshold?: number
}

export function PullToRefresh({
  children,
  onRefresh,
  className,
  disabled = false,
  threshold = 80
}: PullToRefreshProps) {
  const [isPulling, setIsPulling] = React.useState(false)
  const [pullDistance, setPullDistance] = React.useState(0)
  const [isRefreshing, setIsRefreshing] = React.useState(false)
  const [startY, setStartY] = React.useState(0)
  const containerRef = React.useRef<HTMLDivElement>(null)

  const handleTouchStart = React.useCallback((e: TouchEvent) => {
    if (disabled || isRefreshing) return
    
    const container = containerRef.current
    if (!container || container.scrollTop > 0) return
    
    setStartY(e.touches[0].clientY)
    setIsPulling(true)
  }, [disabled, isRefreshing])

  const handleTouchMove = React.useCallback((e: TouchEvent) => {
    if (!isPulling || disabled || isRefreshing) return
    
    const container = containerRef.current
    if (!container || container.scrollTop > 0) return
    
    const currentY = e.touches[0].clientY
    const distance = Math.max(0, currentY - startY)
    
    if (distance > 0) {
      e.preventDefault()
      setPullDistance(Math.min(distance * 0.5, threshold * 1.5))
    }
  }, [isPulling, disabled, isRefreshing, startY, threshold])

  const handleTouchEnd = React.useCallback(async () => {
    if (!isPulling || disabled || isRefreshing) return
    
    setIsPulling(false)
    
    if (pullDistance >= threshold) {
      setIsRefreshing(true)
      try {
        await onRefresh()
      } catch (error) {
        console.error('Refresh failed:', error)
      } finally {
        setIsRefreshing(false)
      }
    }
    
    setPullDistance(0)
  }, [isPulling, disabled, isRefreshing, pullDistance, threshold, onRefresh])

  React.useEffect(() => {
    const container = containerRef.current
    if (!container) return

    container.addEventListener('touchstart', handleTouchStart, { passive: false })
    container.addEventListener('touchmove', handleTouchMove, { passive: false })
    container.addEventListener('touchend', handleTouchEnd)

    return () => {
      container.removeEventListener('touchstart', handleTouchStart)
      container.removeEventListener('touchmove', handleTouchMove)
      container.removeEventListener('touchend', handleTouchEnd)
    }
  }, [handleTouchStart, handleTouchMove, handleTouchEnd])

  const refreshProgress = Math.min(pullDistance / threshold, 1)
  const shouldTrigger = pullDistance >= threshold

  return (
    <div
      ref={containerRef}
      className={cn("relative overflow-auto", className)}
      style={{
        transform: `translateY(${pullDistance}px)`,
        transition: isPulling ? 'none' : 'transform 0.3s ease-out'
      }}
    >
      {/* Pull indicator */}
      <div
        className="absolute top-0 left-0 right-0 flex items-center justify-center bg-orange-50 border-b border-orange-100 transition-all duration-200"
        style={{
          height: `${Math.max(0, pullDistance)}px`,
          opacity: pullDistance > 20 ? 1 : 0
        }}
      >
        <div className="flex items-center space-x-2 text-orange-600">
          <RefreshCw
            className={cn(
              "h-5 w-5 transition-transform duration-200",
              (isRefreshing || shouldTrigger) && "animate-spin"
            )}
            style={{
              transform: `rotate(${refreshProgress * 180}deg)`
            }}
          />
          <span className="text-sm font-medium">
            {isRefreshing
              ? 'Obnovování...'
              : shouldTrigger
              ? 'Uvolněte pro obnovení'
              : 'Táhněte dolů pro obnovení'
            }
          </span>
        </div>
      </div>

      {children}
    </div>
  )
}
