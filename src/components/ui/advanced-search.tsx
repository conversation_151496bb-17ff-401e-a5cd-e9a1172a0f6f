'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { 
  Popover, 
  PopoverContent, 
  PopoverTrigger 
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { 
  Search, 
  Filter, 
  X, 
  Calendar,
  SlidersHorizontal
} from 'lucide-react'
import { DayPicker } from 'react-day-picker'
import { format } from 'date-fns'
import { cs } from 'date-fns/locale'

interface SearchField {
  key: string
  label: string
  type: 'text' | 'select' | 'date' | 'number'
  options?: { value: string; label: string }[]
  placeholder?: string
}

interface SearchFilter {
  key: string
  value: any
  label: string
  displayValue: string
}

interface AdvancedSearchProps {
  fields: SearchField[]
  onSearch: (filters: Record<string, any>) => void
  onClear: () => void
  initialFilters?: Record<string, any>
  placeholder?: string
  className?: string
}

export function AdvancedSearch({
  fields,
  onSearch,
  onClear,
  initialFilters = {},
  placeholder = 'Vyhledat...',
  className = ''
}: AdvancedSearchProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState<Record<string, any>>(initialFilters)
  const [activeFilters, setActiveFilters] = useState<SearchFilter[]>([])

  // Update active filters when filters change
  useEffect(() => {
    const active: SearchFilter[] = []
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== '' && value !== 'all') {
        const field = fields.find(f => f.key === key)
        if (field) {
          let displayValue = value
          
          if (field.type === 'select' && field.options) {
            const option = field.options.find(opt => opt.value === value)
            displayValue = option?.label || value
          } else if (field.type === 'date' && value instanceof Date) {
            displayValue = format(value, 'dd.MM.yyyy', { locale: cs })
          }
          
          active.push({
            key,
            value,
            label: field.label,
            displayValue: String(displayValue)
          })
        }
      }
    })
    
    setActiveFilters(active)
  }, [filters, fields])

  const handleFilterChange = (key: string, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleRemoveFilter = (key: string) => {
    setFilters(prev => {
      const newFilters = { ...prev }
      delete newFilters[key]
      return newFilters
    })
  }

  const handleSearch = () => {
    const searchFilters = { ...filters }
    if (searchTerm.trim()) {
      searchFilters.search = searchTerm.trim()
    }
    onSearch(searchFilters)
    setIsOpen(false)
  }

  const handleClear = () => {
    setSearchTerm('')
    setFilters({})
    onClear()
    setIsOpen(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Main Search Input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="search"
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyDown={handleKeyDown}
          className="pl-10 pr-20 h-12 rounded-xl border-gray-200 focus:border-orange-300 focus:ring-orange-200"
        />
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
          <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-gray-100"
                data-filter-button
              >
                <SlidersHorizontal className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80 p-4" align="end">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">Pokročilé vyhledávání</h4>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="h-6 w-6 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-3">
                  {fields.map((field) => (
                    <div key={field.key} className="space-y-1">
                      <Label htmlFor={field.key} className="text-sm font-medium">
                        {field.label}
                      </Label>
                      
                      {field.type === 'text' && (
                        <Input
                          id={field.key}
                          placeholder={field.placeholder}
                          value={filters[field.key] || ''}
                          onChange={(e) => handleFilterChange(field.key, e.target.value)}
                          className="h-9"
                        />
                      )}
                      
                      {field.type === 'number' && (
                        <Input
                          id={field.key}
                          type="number"
                          placeholder={field.placeholder}
                          value={filters[field.key] || ''}
                          onChange={(e) => handleFilterChange(field.key, e.target.value)}
                          className="h-9"
                        />
                      )}
                      
                      {field.type === 'select' && field.options && (
                        <Select
                          value={filters[field.key] || ''}
                          onValueChange={(value) => handleFilterChange(field.key, value)}
                        >
                          <SelectTrigger className="h-9">
                            <SelectValue placeholder="Vyberte..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="">Všechny</SelectItem>
                            {field.options.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                      
                      {field.type === 'date' && (
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-full h-9 justify-start text-left font-normal"
                            >
                              <Calendar className="mr-2 h-4 w-4" />
                              {filters[field.key] ? (
                                format(filters[field.key], 'dd.MM.yyyy', { locale: cs })
                              ) : (
                                <span>Vyberte datum</span>
                              )}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <DayPicker
                              mode="single"
                              selected={filters[field.key]}
                              onSelect={(date) => handleFilterChange(field.key, date)}
                              locale={cs}
                              className="p-3"
                            />
                          </PopoverContent>
                        </Popover>
                      )}
                    </div>
                  ))}
                </div>
                
                <div className="flex justify-between pt-3 border-t">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClear}
                    className="text-gray-600"
                  >
                    Vymazat
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSearch}
                    className="bg-orange-600 hover:bg-orange-700"
                  >
                    Vyhledat
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>
          
          <Button
            size="sm"
            onClick={handleSearch}
            className="h-8 bg-orange-600 hover:bg-orange-700 text-white"
          >
            <Search className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Active Filters */}
      {activeFilters.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {activeFilters.map((filter) => (
            <Badge
              key={filter.key}
              variant="secondary"
              className="bg-orange-100 text-orange-800 hover:bg-orange-200 cursor-pointer"
              onClick={() => handleRemoveFilter(filter.key)}
            >
              {filter.label}: {filter.displayValue}
              <X className="ml-1 h-3 w-3" />
            </Badge>
          ))}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="h-6 text-xs text-gray-500 hover:text-gray-700"
          >
            Vymazat vše
          </Button>
        </div>
      )}
    </div>
  )
}
