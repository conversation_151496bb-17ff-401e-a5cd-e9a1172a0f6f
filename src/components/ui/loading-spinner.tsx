import { cn } from '@/lib/utils'
import type { LoadingSpinnerProps } from '@/types'

export function LoadingSpinner({ 
  size = 'md', 
  message, 
  className 
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  }

  const containerClasses = {
    sm: 'min-h-[100px]',
    md: 'min-h-[200px]',
    lg: 'min-h-[400px]'
  }

  return (
    <div className={cn(
      "flex items-center justify-center",
      containerClasses[size],
      className
    )}>
      <div className="text-center">
        <div className={cn(
          "animate-spin rounded-full border-b-2 border-orange-500 mx-auto mb-4",
          sizeClasses[size]
        )}></div>
        {message && (
          <p className="text-gray-600 text-sm">{message}</p>
        )}
      </div>
    </div>
  )
}

// Specialized loading components
export function PageLoadingSpinner({ message = "Načítám..." }: { message?: string }) {
  return (
    <LoadingSpinner 
      size="lg" 
      message={message}
      className="min-h-[400px]"
    />
  )
}

export function ComponentLoadingSpinner({ message }: { message?: string }) {
  return (
    <LoadingSpinner 
      size="md" 
      message={message}
      className="min-h-[200px]"
    />
  )
}

export function InlineLoadingSpinner({ message }: { message?: string }) {
  return (
    <LoadingSpinner 
      size="sm" 
      message={message}
      className="min-h-[60px]"
    />
  )
}

// Button loading spinner
export function ButtonLoadingSpinner() {
  return (
    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
  )
}
