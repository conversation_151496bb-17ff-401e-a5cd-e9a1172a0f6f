'use client'

import * as React from "react"
import { cn } from "@/lib/utils"

interface SwipeAction {
  icon: React.ReactNode
  label: string
  onClick: () => void
  className?: string
  side: 'left' | 'right'
}

interface SwipeActionsProps {
  children: React.ReactNode
  actions: SwipeAction[]
  className?: string
  threshold?: number
}

export function SwipeActions({
  children,
  actions,
  className,
  threshold = 80
}: SwipeActionsProps) {
  const [swipeDistance, setSwipeDistance] = React.useState(0)
  const [isSwipping, setIsSwipping] = React.useState(false)
  const [startX, setStartX] = React.useState(0)
  const [activeAction, setActiveAction] = React.useState<SwipeAction | null>(null)
  const containerRef = React.useRef<HTMLDivElement>(null)

  const leftActions = actions.filter(action => action.side === 'left')
  const rightActions = actions.filter(action => action.side === 'right')

  const handleTouchStart = React.useCallback((e: TouchEvent) => {
    setStartX(e.touches[0].clientX)
    setIsSwipping(true)
  }, [])

  const handleTouchMove = React.useCallback((e: TouchEvent) => {
    if (!isSwipping) return
    
    const currentX = e.touches[0].clientX
    const distance = currentX - startX
    
    // Limit swipe distance
    const maxDistance = 120
    const limitedDistance = Math.max(-maxDistance, Math.min(maxDistance, distance))
    
    setSwipeDistance(limitedDistance)
    
    // Determine active action
    if (Math.abs(limitedDistance) >= threshold) {
      if (limitedDistance > 0 && leftActions.length > 0) {
        setActiveAction(leftActions[0])
      } else if (limitedDistance < 0 && rightActions.length > 0) {
        setActiveAction(rightActions[0])
      }
    } else {
      setActiveAction(null)
    }
  }, [isSwipping, startX, threshold, leftActions, rightActions])

  const handleTouchEnd = React.useCallback(() => {
    setIsSwipping(false)
    
    if (activeAction && Math.abs(swipeDistance) >= threshold) {
      activeAction.onClick()
    }
    
    setSwipeDistance(0)
    setActiveAction(null)
  }, [activeAction, swipeDistance, threshold])

  React.useEffect(() => {
    const container = containerRef.current
    if (!container) return

    container.addEventListener('touchstart', handleTouchStart, { passive: true })
    container.addEventListener('touchmove', handleTouchMove, { passive: true })
    container.addEventListener('touchend', handleTouchEnd, { passive: true })

    return () => {
      container.removeEventListener('touchstart', handleTouchStart)
      container.removeEventListener('touchmove', handleTouchMove)
      container.removeEventListener('touchend', handleTouchEnd)
    }
  }, [handleTouchStart, handleTouchMove, handleTouchEnd])

  return (
    <div
      ref={containerRef}
      className={cn("relative overflow-hidden", className)}
    >
      {/* Left actions */}
      {leftActions.length > 0 && (
        <div
          className="absolute left-0 top-0 bottom-0 flex items-center justify-start bg-green-500"
          style={{
            width: `${Math.max(0, swipeDistance)}px`,
            opacity: swipeDistance > 20 ? 1 : 0
          }}
        >
          {leftActions.map((action, index) => (
            <div
              key={index}
              className={cn(
                "flex flex-col items-center justify-center h-full px-4 text-white transition-all duration-200",
                activeAction === action && "bg-green-600",
                action.className
              )}
            >
              {action.icon}
              <span className="text-xs mt-1">{action.label}</span>
            </div>
          ))}
        </div>
      )}

      {/* Right actions */}
      {rightActions.length > 0 && (
        <div
          className="absolute right-0 top-0 bottom-0 flex items-center justify-end bg-red-500"
          style={{
            width: `${Math.max(0, -swipeDistance)}px`,
            opacity: swipeDistance < -20 ? 1 : 0
          }}
        >
          {rightActions.map((action, index) => (
            <div
              key={index}
              className={cn(
                "flex flex-col items-center justify-center h-full px-4 text-white transition-all duration-200",
                activeAction === action && "bg-red-600",
                action.className
              )}
            >
              {action.icon}
              <span className="text-xs mt-1">{action.label}</span>
            </div>
          ))}
        </div>
      )}

      {/* Content */}
      <div
        className="relative bg-white transition-transform duration-200"
        style={{
          transform: `translateX(${swipeDistance}px)`,
          transition: isSwipping ? 'none' : 'transform 0.3s ease-out'
        }}
      >
        {children}
      </div>
    </div>
  )
}
