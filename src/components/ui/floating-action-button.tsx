'use client'

import * as React from "react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Plus, X } from "lucide-react"

interface FloatingActionButtonProps {
  children?: React.ReactNode
  className?: string
  onClick?: () => void
  icon?: React.ReactNode
  expanded?: boolean
  actions?: Array<{
    icon: React.ReactNode
    label: string
    onClick: () => void
    className?: string
  }>
}

export function FloatingActionButton({
  children,
  className,
  onClick,
  icon = <Plus className="h-6 w-6" />,
  expanded = false,
  actions = []
}: FloatingActionButtonProps) {
  const [isExpanded, setIsExpanded] = React.useState(expanded)

  const handleMainClick = () => {
    if (actions.length > 0) {
      setIsExpanded(!isExpanded)
    } else if (onClick) {
      onClick()
    }
  }

  return (
    <div className="fixed bottom-6 right-6 z-50 md:hidden">
      {/* Action buttons */}
      {actions.length > 0 && isExpanded && (
        <div className="absolute bottom-16 right-0 space-y-3 mb-2">
          {actions.map((action, index) => (
            <div
              key={index}
              className="flex items-center space-x-3 animate-in slide-in-from-bottom-2 duration-200"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <span className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg">
                {action.label}
              </span>
              <Button
                size="icon"
                onClick={() => {
                  action.onClick()
                  setIsExpanded(false)
                }}
                className={cn(
                  "h-12 w-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 touch-manipulation active:scale-90",
                  action.className || "bg-orange-500 hover:bg-orange-600 text-white"
                )}
              >
                {action.icon}
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* Backdrop */}
      {isExpanded && (
        <div
          className="fixed inset-0 bg-black bg-opacity-20 -z-10"
          onClick={() => setIsExpanded(false)}
        />
      )}

      {/* Main FAB */}
      <Button
        size="icon"
        onClick={handleMainClick}
        className={cn(
          "h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 touch-manipulation active:scale-90 gradient-orange",
          isExpanded && "rotate-45",
          className
        )}
      >
        {children || (isExpanded && actions.length > 0 ? <X className="h-6 w-6" /> : icon)}
      </Button>
    </div>
  )
}

// Preset FAB for common actions
export function DeviceActionsFAB() {
  const actions = [
    {
      icon: <Plus className="h-5 w-5" />,
      label: "Přidat zařízení",
      onClick: () => window.location.href = "/dashboard/devices/new"
    }
  ]

  return (
    <FloatingActionButton
      actions={actions}
      icon={<Plus className="h-6 w-6" />}
    />
  )
}
