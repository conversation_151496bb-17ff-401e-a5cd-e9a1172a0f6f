'use client'

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  Monitor,
  Building2,
  Users,
  Settings
} from "lucide-react"

interface BottomNavItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: number
}

interface BottomNavigationProps {
  items?: BottomNavItem[]
  className?: string
}

const defaultItems: BottomNavItem[] = [
  { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
  { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>í', href: '/dashboard/devices', icon: Monitor },
  { name: 'Uživatelé', href: '/dashboard/users', icon: Users },
  { name: 'Nastavení', href: '/dashboard/settings', icon: Settings },
]

export function BottomNavigation({ 
  items = defaultItems, 
  className 
}: BottomNavigationProps) {
  const pathname = usePathname()

  return (
    <div className={cn(
      "fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-40 md:hidden",
      className
    )}>
      <div className="grid grid-cols-5 h-16">
        {items.map((item) => {
          const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
          const Icon = item.icon
          
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "flex flex-col items-center justify-center space-y-1 transition-all duration-200 touch-manipulation active:scale-95 relative",
                isActive
                  ? "text-orange-600 bg-orange-50"
                  : "text-gray-500 hover:text-gray-700 active:bg-gray-100"
              )}
            >
              <div className="relative">
                <Icon className={cn(
                  "h-5 w-5 transition-colors duration-200",
                  isActive ? "text-orange-600" : "text-gray-500"
                )} />
                {item.badge && item.badge > 0 && (
                  <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                    {item.badge > 99 ? '99+' : item.badge}
                  </span>
                )}
              </div>
              <span className={cn(
                "text-xs font-medium transition-colors duration-200",
                isActive ? "text-orange-600" : "text-gray-500"
              )}>
                {item.name}
              </span>
              {isActive && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-orange-600 rounded-b-full" />
              )}
            </Link>
          )
        })}
      </div>
    </div>
  )
}

// Spacer component to prevent content from being hidden behind bottom nav
export function BottomNavigationSpacer({ className }: { className?: string }) {
  return (
    <div className={cn("h-16 md:hidden", className)} />
  )
}
