'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import { Badge } from '@/components/ui/badge'
import { 
  ChevronDown, 
  Trash2, 
  Edit, 
  Archive, 
  Download, 
  Tag,
  CheckSquare,
  Square
} from 'lucide-react'
import { useBulkOperationsShortcuts } from '@/hooks/use-keyboard-shortcuts'

interface BulkOperation {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  action: (selectedIds: string[]) => void
  variant?: 'default' | 'destructive' | 'secondary'
  requiresConfirmation?: boolean
}

interface BulkOperationsProps {
  selectedIds: string[]
  totalItems: number
  onSelectAll: () => void
  onDeselectAll: () => void
  operations: BulkOperation[]
  className?: string
}

export function BulkOperations({
  selectedIds,
  totalItems,
  onSelectAll,
  onDeselectAll,
  operations,
  className = ''
}: BulkOperationsProps) {
  const [isOpen, setIsOpen] = useState(false)

  // Setup keyboard shortcuts
  useBulkOperationsShortcuts(
    onSelectAll,
    onDeselectAll,
    () => {
      const deleteOperation = operations.find(op => op.id === 'delete')
      if (deleteOperation && selectedIds.length > 0) {
        deleteOperation.action(selectedIds)
      }
    }
  )

  const isAllSelected = selectedIds.length === totalItems && totalItems > 0
  const isPartiallySelected = selectedIds.length > 0 && selectedIds.length < totalItems

  const handleSelectAllToggle = () => {
    if (isAllSelected || isPartiallySelected) {
      onDeselectAll()
    } else {
      onSelectAll()
    }
  }

  const handleOperationClick = (operation: BulkOperation) => {
    if (selectedIds.length === 0) return
    
    if (operation.requiresConfirmation) {
      const confirmed = window.confirm(
        `Opravdu chcete provést akci "${operation.label}" na ${selectedIds.length} položkách?`
      )
      if (!confirmed) return
    }
    
    operation.action(selectedIds)
    setIsOpen(false)
  }

  if (totalItems === 0) {
    return null
  }

  return (
    <div className={`flex items-center gap-3 p-3 bg-gray-50 border border-gray-200 rounded-lg ${className}`}>
      {/* Select All Checkbox */}
      <div className="flex items-center gap-2">
        <Checkbox
          checked={isAllSelected}
          ref={(el) => {
            if (el) {
              el.indeterminate = isPartiallySelected
            }
          }}
          onCheckedChange={handleSelectAllToggle}
          className="data-[state=checked]:bg-orange-600 data-[state=checked]:border-orange-600"
        />
        <span className="text-sm text-gray-600">
          {isAllSelected ? 'Zrušit výběr všech' : 'Vybrat vše'}
        </span>
      </div>

      {/* Selected Count */}
      {selectedIds.length > 0 && (
        <Badge variant="secondary" className="bg-orange-100 text-orange-800">
          {selectedIds.length} vybráno
        </Badge>
      )}

      {/* Bulk Operations Dropdown */}
      {selectedIds.length > 0 && operations.length > 0 && (
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="ml-auto border-orange-200 hover:bg-orange-50"
            >
              Hromadné akce
              <ChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            {operations.map((operation, index) => {
              const Icon = operation.icon
              const isDestructive = operation.variant === 'destructive'
              
              return (
                <div key={operation.id}>
                  <DropdownMenuItem
                    onClick={() => handleOperationClick(operation)}
                    className={`flex items-center gap-2 cursor-pointer ${
                      isDestructive 
                        ? 'text-red-600 hover:text-red-700 hover:bg-red-50' 
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    {operation.label}
                  </DropdownMenuItem>
                  {index < operations.length - 1 && operation.variant === 'destructive' && (
                    <DropdownMenuSeparator />
                  )}
                </div>
              )
            })}
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      {/* Quick Actions */}
      {selectedIds.length > 0 && (
        <div className="flex items-center gap-1 ml-2">
          {operations.slice(0, 2).map((operation) => {
            const Icon = operation.icon
            return (
              <Button
                key={operation.id}
                variant="ghost"
                size="sm"
                onClick={() => handleOperationClick(operation)}
                className={`h-8 w-8 p-0 ${
                  operation.variant === 'destructive'
                    ? 'hover:bg-red-50 hover:text-red-600'
                    : 'hover:bg-gray-100'
                }`}
                title={operation.label}
              >
                <Icon className="h-4 w-4" />
              </Button>
            )
          })}
        </div>
      )}
    </div>
  )
}

// Predefined bulk operations for common use cases
export const commonBulkOperations = {
  delete: (onDelete: (ids: string[]) => void): BulkOperation => ({
    id: 'delete',
    label: 'Smazat',
    icon: Trash2,
    action: onDelete,
    variant: 'destructive',
    requiresConfirmation: true
  }),

  edit: (onEdit: (ids: string[]) => void): BulkOperation => ({
    id: 'edit',
    label: 'Upravit',
    icon: Edit,
    action: onEdit,
    variant: 'default'
  }),

  archive: (onArchive: (ids: string[]) => void): BulkOperation => ({
    id: 'archive',
    label: 'Archivovat',
    icon: Archive,
    action: onArchive,
    variant: 'secondary'
  }),

  export: (onExport: (ids: string[]) => void): BulkOperation => ({
    id: 'export',
    label: 'Exportovat',
    icon: Download,
    action: onExport,
    variant: 'default'
  }),

  tag: (onTag: (ids: string[]) => void): BulkOperation => ({
    id: 'tag',
    label: 'Přidat štítek',
    icon: Tag,
    action: onTag,
    variant: 'default'
  })
}

// Hook for managing bulk selection state
export function useBulkSelection<T extends { id: string }>(items: T[]) {
  const [selectedIds, setSelectedIds] = useState<string[]>([])

  const selectAll = () => {
    setSelectedIds(items.map(item => item.id))
  }

  const deselectAll = () => {
    setSelectedIds([])
  }

  const toggleSelection = (id: string) => {
    setSelectedIds(prev => 
      prev.includes(id) 
        ? prev.filter(selectedId => selectedId !== id)
        : [...prev, id]
    )
  }

  const isSelected = (id: string) => selectedIds.includes(id)

  const selectedItems = items.filter(item => selectedIds.includes(item.id))

  return {
    selectedIds,
    selectedItems,
    selectAll,
    deselectAll,
    toggleSelection,
    isSelected,
    setSelectedIds
  }
}
