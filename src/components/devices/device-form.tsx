'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Save, X } from 'lucide-react'

interface DeviceFormData {
  name: string
  type: string
  brand: string
  model: string
  serialNumber: string
  status: string
  location: string
  assignedTo: string
  purchaseDate: string
  warrantyUntil: string
  notes: string
}

interface DeviceFormProps {
  initialData?: Partial<DeviceFormData>
  onSubmit: (data: DeviceFormData) => Promise<void>
  onCancel: () => void
  submitLabel?: string
  loading?: boolean
}

const deviceTypes = [
  { value: 'laptop', label: 'Notebook' },
  { value: 'desktop', label: '<PERSON><PERSON><PERSON><PERSON>' },
  { value: 'server', label: 'Server' },
  { value: 'mobile', label: 'Mobilní telefon' },
  { value: 'tablet', label: 'Tablet' },
  { value: 'printer', label: 'Tiskárna' },
  { value: 'monitor', label: 'Monitor' },
  { value: 'network', label: 'Síťové zařízení' },
  { value: 'other', label: 'Ostatní' }
]

const deviceStatuses = [
  { value: 'active', label: 'Aktivní' },
  { value: 'inactive', label: 'Neaktivní' },
  { value: 'maintenance', label: 'Údržba' },
  { value: 'retired', label: 'Vyřazeno' }
]

export default function DeviceForm({
  initialData = {},
  onSubmit,
  onCancel,
  submitLabel = 'Uložit',
  loading = false
}: DeviceFormProps) {
  const [formData, setFormData] = useState<DeviceFormData>({
    name: initialData.name || '',
    type: initialData.type || 'laptop',
    brand: initialData.brand || '',
    model: initialData.model || '',
    serialNumber: initialData.serialNumber || '',
    status: initialData.status || 'active',
    location: initialData.location || '',
    assignedTo: initialData.assignedTo || '',
    purchaseDate: initialData.purchaseDate || '',
    warrantyUntil: initialData.warrantyUntil || '',
    notes: initialData.notes || ''
  })
  const [error, setError] = useState('')

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    // Validation
    if (!formData.name.trim()) {
      setError('Název zařízení je povinný')
      return
    }
    if (!formData.brand.trim()) {
      setError('Značka je povinná')
      return
    }
    if (!formData.model.trim()) {
      setError('Model je povinný')
      return
    }
    if (!formData.location.trim()) {
      setError('Umístění je povinné')
      return
    }

    try {
      await onSubmit(formData)
    } catch (err) {
      setError('Došlo k chybě při ukládání')
    }
  }

  return (
    <Card className="max-w-2xl">
      <CardHeader>
        <CardTitle>Informace o zařízení</CardTitle>
        <CardDescription>
          Vyplňte základní údaje o zařízení
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Základní údaje</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Název zařízení *</Label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  placeholder="Notebook Dell Latitude"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Typ zařízení *</Label>
                <select
                  id="type"
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  required
                  className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {deviceTypes.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="brand">Značka *</Label>
                <Input
                  id="brand"
                  name="brand"
                  type="text"
                  placeholder="Dell"
                  value={formData.brand}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="model">Model *</Label>
                <Input
                  id="model"
                  name="model"
                  type="text"
                  placeholder="Latitude 5520"
                  value={formData.model}
                  onChange={handleInputChange}
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="serialNumber">Sériové číslo</Label>
                <Input
                  id="serialNumber"
                  name="serialNumber"
                  type="text"
                  placeholder="ABC123456789"
                  value={formData.serialNumber}
                  onChange={handleInputChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Stav *</Label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  required
                  className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  {deviceStatuses.map(status => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Location & Assignment */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Umístění a přiřazení</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="location">Umístění *</Label>
                <Input
                  id="location"
                  name="location"
                  type="text"
                  placeholder="Kancelář 201"
                  value={formData.location}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="assignedTo">Přiřazeno uživateli</Label>
                <Input
                  id="assignedTo"
                  name="assignedTo"
                  type="text"
                  placeholder="Jan Novák"
                  value={formData.assignedTo}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>

          {/* Dates */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Datumy</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="purchaseDate">Datum nákupu</Label>
                <Input
                  id="purchaseDate"
                  name="purchaseDate"
                  type="date"
                  value={formData.purchaseDate}
                  onChange={handleInputChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="warrantyUntil">Záruka do</Label>
                <Input
                  id="warrantyUntil"
                  name="warrantyUntil"
                  type="date"
                  value={formData.warrantyUntil}
                  onChange={handleInputChange}
                />
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Poznámky</h3>
            
            <div className="space-y-2">
              <Label htmlFor="notes">Dodatečné informace</Label>
              <Textarea
                id="notes"
                name="notes"
                placeholder="Poznámky, konfigurace, historie oprav..."
                value={formData.notes}
                onChange={handleInputChange}
                rows={4}
              />
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-4 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              <X className="h-4 w-4 mr-2" />
              Zrušit
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="h-4 w-4 mr-2" />
              {loading ? 'Ukládám...' : submitLabel}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
