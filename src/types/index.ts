// Centrální TypeScript typy pro celou aplikaci
export type DeviceStatus = 'aktivni' | 'zapujceno' | 'servis' | 'vyrazeno'
export type UserRole = 'admin' | 'technik' | 'ctenar'

// User types
export interface User {
  id: string
  email: string
  name: string | null
  isSuperAdmin: boolean
  lastLogin?: Date | null
  createdAt: Date
  updatedAt: Date
}

export interface UserWithOrganizations extends User {
  organizationMembers: Array<{
    id: string
    role: UserRole
    organization: {
      id: string
      name: string
      slug: string
    }
  }>
}

// Organization types
export interface Organization {
  id: string
  name: string
  slug: string
  description?: string | null
  createdAt: Date
  updatedAt: Date
}

export interface OrganizationWithMembers extends Organization {
  members: Array<{
    id: string
    role: UserRole
    user: {
      id: string
      email: string
      name: string | null
    }
  }>
  devices: Array<{
    id: string
    status: DeviceStatus
  }>
}

// Device types
export interface Device {
  id: string
  evidenceNumber: string
  name: string
  description?: string | null
  deviceType: string
  manufacturer: string
  model: string
  serialNumber?: string | null
  location: string
  status: DeviceStatus
  purchaseDate?: Date | null
  warrantyExpiry?: Date | null
  notes?: string | null
  organizationId: string
  createdAt: Date
  updatedAt: Date
}

export interface DeviceWithCounts extends Device {
  _count: {
    files: number
    history: number
  }
}

export interface DeviceWithDetails extends Device {
  organization: Organization
  files: DeviceFile[]
  history: DeviceHistory[]
}

// Device File types
export interface DeviceFile {
  id: string
  filename: string
  originalName: string
  size: number
  mimeType: string
  deviceId: string
  uploadedAt: Date
}

// Device History types
export interface DeviceHistory {
  id: string
  deviceId: string
  action: string
  oldValues?: string | null
  newValues?: string | null
  changedBy: string
  createdAt: Date
}

// Session types
export interface Session {
  id: string
  userId: string
  token: string
  expiresAt: Date
  createdAt: Date
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface ApiError {
  error: string
  statusCode?: number
  code?: string
}

// Form types
export interface DeviceFormData {
  evidenceNumber: string
  name: string
  description?: string
  deviceType: string
  manufacturer: string
  model: string
  serialNumber?: string
  location: string
  status: DeviceStatus
  purchaseDate?: string
  warrantyExpiry?: string
  notes?: string
  organizationId: string
}

export interface UserFormData {
  name: string
  email: string
  password?: string
  role: UserRole
}

export interface OrganizationFormData {
  name: string
  slug: string
  description?: string
  adminEmail?: string
}

// Stats types
export interface DeviceStats {
  total: number
  aktivni: number
  zapujceno: number
  servis: number
  vyrazeno: number
  byLocation: Record<string, number>
  byType: Record<string, number>
  recentlyAdded: number
}

export interface DashboardStats {
  totalDevices: number
  activeDevices: number
  maintenanceDevices: number
  retiredDevices: number
  totalUsers: number
}

// Component Props types
export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  message?: string
  className?: string
}

export interface OrganizationSwitcherProps {
  organizations: Array<{
    id: string
    role: UserRole
    organization: {
      id: string
      name: string
      slug: string
    }
  }>
  isSuperAdmin?: boolean
  allOrganizations?: Array<{
    id: string
    name: string
    slug: string
  }>
}

// Navigation types
export interface NavigationItem {
  name: string
  href: string
  icon: any
}

// Search and Filter types
export interface DeviceFilters {
  search?: string
  status?: DeviceStatus | 'all'
  location?: string
  deviceType?: string
  organizationId?: string
}

export interface SearchParams {
  q?: string
  status?: string
  location?: string
  type?: string
  org?: string
  page?: string
  limit?: string
}
