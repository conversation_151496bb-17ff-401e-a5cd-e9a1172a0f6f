'use client'

import { useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'

interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  metaKey?: boolean
  action: () => void
  description: string
  category?: string
}

interface UseKeyboardShortcutsOptions {
  enabled?: boolean
  preventDefault?: boolean
}

export function useKeyboardShortcuts(
  shortcuts: KeyboardShortcut[],
  options: UseKeyboardShortcutsOptions = {}
) {
  const { enabled = true, preventDefault = true } = options

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!enabled) return

      // Don't trigger shortcuts when typing in inputs
      const target = event.target as HTMLElement
      if (
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.contentEditable === 'true'
      ) {
        return
      }

      const matchingShortcut = shortcuts.find((shortcut) => {
        return (
          event.key.toLowerCase() === shortcut.key.toLowerCase() &&
          !!event.ctrlKey === !!shortcut.ctrlKey &&
          !!event.altKey === !!shortcut.altKey &&
          !!event.shiftKey === !!shortcut.shiftKey &&
          !!event.metaKey === !!shortcut.metaKey
        )
      })

      if (matchingShortcut) {
        if (preventDefault) {
          event.preventDefault()
        }
        matchingShortcut.action()
      }
    },
    [shortcuts, enabled, preventDefault]
  )

  useEffect(() => {
    if (enabled) {
      document.addEventListener('keydown', handleKeyDown)
      return () => document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown, enabled])

  return shortcuts
}

// Global keyboard shortcuts hook
export function useGlobalKeyboardShortcuts() {
  const router = useRouter()

  const shortcuts: KeyboardShortcut[] = [
    // Navigation shortcuts
    {
      key: 'd',
      altKey: true,
      action: () => router.push('/dashboard'),
      description: 'Přejít na dashboard',
      category: 'Navigace'
    },
    {
      key: 'n',
      altKey: true,
      action: () => router.push('/dashboard/devices/new'),
      description: 'Přidat nové zařízení',
      category: 'Zařízení'
    },
    {
      key: 'l',
      altKey: true,
      action: () => router.push('/dashboard/devices'),
      description: 'Seznam zařízení',
      category: 'Zařízení'
    },
    {
      key: 'u',
      altKey: true,
      action: () => router.push('/dashboard/users'),
      description: 'Správa uživatelů',
      category: 'Správa'
    },
    {
      key: 'o',
      altKey: true,
      action: () => router.push('/dashboard/organizations'),
      description: 'Správa organizací',
      category: 'Správa'
    },
    {
      key: 's',
      altKey: true,
      action: () => router.push('/dashboard/settings'),
      description: 'Nastavení',
      category: 'Správa'
    },
    // Search shortcut
    {
      key: '/',
      action: () => {
        const searchInput = document.querySelector('input[type="search"]') as HTMLInputElement
        if (searchInput) {
          searchInput.focus()
        }
      },
      description: 'Zaměřit vyhledávání',
      category: 'Vyhledávání'
    },
    // Help shortcut
    {
      key: '?',
      shiftKey: true,
      action: () => {
        // Show keyboard shortcuts modal
        const event = new CustomEvent('show-shortcuts-modal')
        window.dispatchEvent(event)
      },
      description: 'Zobrazit klávesové zkratky',
      category: 'Nápověda'
    },
    // Escape to close modals
    {
      key: 'Escape',
      action: () => {
        const event = new CustomEvent('close-modals')
        window.dispatchEvent(event)
      },
      description: 'Zavřít modály',
      category: 'Navigace'
    }
  ]

  return useKeyboardShortcuts(shortcuts)
}

// Device list specific shortcuts
export function useDeviceListShortcuts() {
  const router = useRouter()

  const shortcuts: KeyboardShortcut[] = [
    {
      key: 'n',
      ctrlKey: true,
      action: () => router.push('/dashboard/devices/new'),
      description: 'Nové zařízení',
      category: 'Zařízení'
    },
    {
      key: 'r',
      ctrlKey: true,
      action: () => window.location.reload(),
      description: 'Obnovit seznam',
      category: 'Akce'
    },
    {
      key: 'f',
      ctrlKey: true,
      action: () => {
        const filterButton = document.querySelector('[data-filter-button]') as HTMLButtonElement
        if (filterButton) {
          filterButton.click()
        }
      },
      description: 'Otevřít filtry',
      category: 'Filtrování'
    }
  ]

  return useKeyboardShortcuts(shortcuts)
}

// Form shortcuts
export function useFormShortcuts(onSave?: () => void, onCancel?: () => void) {
  const shortcuts: KeyboardShortcut[] = []

  if (onSave) {
    shortcuts.push({
      key: 's',
      ctrlKey: true,
      action: onSave,
      description: 'Uložit formulář',
      category: 'Formulář'
    })
  }

  if (onCancel) {
    shortcuts.push({
      key: 'Escape',
      action: onCancel,
      description: 'Zrušit úpravy',
      category: 'Formulář'
    })
  }

  return useKeyboardShortcuts(shortcuts)
}

// Modal shortcuts
export function useModalShortcuts(onClose?: () => void) {
  const shortcuts: KeyboardShortcut[] = []

  if (onClose) {
    shortcuts.push({
      key: 'Escape',
      action: onClose,
      description: 'Zavřít modal',
      category: 'Modal'
    })
  }

  return useKeyboardShortcuts(shortcuts)
}

// Bulk operations shortcuts
export function useBulkOperationsShortcuts(
  onSelectAll?: () => void,
  onDeselectAll?: () => void,
  onDelete?: () => void
) {
  const shortcuts: KeyboardShortcut[] = []

  if (onSelectAll) {
    shortcuts.push({
      key: 'a',
      ctrlKey: true,
      action: onSelectAll,
      description: 'Vybrat vše',
      category: 'Výběr'
    })
  }

  if (onDeselectAll) {
    shortcuts.push({
      key: 'd',
      ctrlKey: true,
      action: onDeselectAll,
      description: 'Zrušit výběr',
      category: 'Výběr'
    })
  }

  if (onDelete) {
    shortcuts.push({
      key: 'Delete',
      action: onDelete,
      description: 'Smazat vybrané',
      category: 'Akce'
    })
  }

  return useKeyboardShortcuts(shortcuts)
}

// Get all available shortcuts for help display
export function getAllShortcuts(): Record<string, KeyboardShortcut[]> {
  // Define shortcuts directly instead of using hook
  const globalShortcuts: KeyboardShortcut[] = [
    // Navigation shortcuts
    {
      key: 'd',
      altKey: true,
      action: () => {},
      description: 'Přejít na dashboard',
      category: 'Navigace'
    },
    {
      key: 'n',
      altKey: true,
      action: () => {},
      description: 'Přidat nové zařízení',
      category: 'Zařízení'
    },
    {
      key: 'l',
      altKey: true,
      action: () => {},
      description: 'Seznam zařízení',
      category: 'Zařízení'
    },
    {
      key: 'u',
      altKey: true,
      action: () => {},
      description: 'Správa uživatelů',
      category: 'Správa'
    },
    {
      key: 'o',
      altKey: true,
      action: () => {},
      description: 'Správa organizací',
      category: 'Správa'
    },
    {
      key: 's',
      altKey: true,
      action: () => {},
      description: 'Nastavení',
      category: 'Správa'
    },
    // Search shortcut
    {
      key: '/',
      action: () => {},
      description: 'Zaměřit vyhledávání',
      category: 'Vyhledávání'
    },
    // Help shortcut
    {
      key: '?',
      shiftKey: true,
      action: () => {},
      description: 'Zobrazit klávesové zkratky',
      category: 'Nápověda'
    },
    // Escape to close modals
    {
      key: 'Escape',
      action: () => {},
      description: 'Zavřít modály',
      category: 'Navigace'
    }
  ]

  const categorizedShortcuts: Record<string, KeyboardShortcut[]> = {}

  globalShortcuts.forEach(shortcut => {
    const category = shortcut.category || 'Ostatní'
    if (!categorizedShortcuts[category]) {
      categorizedShortcuts[category] = []
    }
    categorizedShortcuts[category].push(shortcut)
  })

  return categorizedShortcuts
}

// Format shortcut for display
export function formatShortcut(shortcut: KeyboardShortcut): string {
  const parts: string[] = []
  
  if (shortcut.ctrlKey) parts.push('Ctrl')
  if (shortcut.altKey) parts.push('Alt')
  if (shortcut.shiftKey) parts.push('Shift')
  if (shortcut.metaKey) parts.push('Cmd')
  
  parts.push(shortcut.key.toUpperCase())
  
  return parts.join(' + ')
}
