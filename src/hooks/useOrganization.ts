'use client'

import { useSearchParams } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'

interface Organization {
  id: string
  name: string
  slug: string
}

interface UserInfo {
  id: string
  email: string
  name: string | null
  isSuperAdmin: boolean
  organizations: Array<{
    id: string
    role: string
    organization: Organization
  }>
}

export function useOrganization() {
  const searchParams = useSearchParams()
  
  // Fetch user info
  const { data: userInfo } = useQuery({
    queryKey: ['user'],
    queryFn: async () => {
      const response = await fetch('/api/auth/me')
      if (!response.ok) throw new Error('Failed to fetch user')
      const data = await response.json()
      return data.user as UserInfo
    },
  })

  // Get current organization from URL params or default to first organization
  const orgParam = searchParams.get('org')
  
  let currentOrganization: Organization | null = null
  
  if (userInfo) {
    if (userInfo.isSuperAdmin && orgParam) {
      // For superadmin, we need to fetch the organization by ID
      // For now, we'll use the first organization as fallback
      currentOrganization = userInfo.organizations[0]?.organization || null
    } else {
      // For regular users, find the organization from their memberships
      const membership = orgParam 
        ? userInfo.organizations.find(org => org.organization.id === orgParam)
        : userInfo.organizations[0]
      
      currentOrganization = membership?.organization || null
    }
  }

  return {
    currentOrganization,
    userInfo,
    isLoading: !userInfo,
  }
}
