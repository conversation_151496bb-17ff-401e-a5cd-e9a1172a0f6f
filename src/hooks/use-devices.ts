'use client'

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { queryKeys, invalidateQueries, optimisticUpdates } from '@/lib/react-query'
import { apiGet, apiPost, apiPut, apiDelete } from '@/lib/api-response'
import type { Device, DeviceWithCounts, DeviceWithDetails, DeviceFormData, DeviceFilters } from '@/types'
import { SUCCESS_MESSAGES } from '@/lib/constants'

// Get devices list
export function useDevices(filters?: DeviceFilters) {
  return useQuery({
    queryKey: queryKeys.devicesList(filters),
    queryFn: async () => {
      const url = new URL('/api/devices', window.location.origin)
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value && value !== 'all') {
            url.searchParams.set(key, String(value))
          }
        })
      }
      
      const response = await fetch(url.toString())
      if (!response.ok) {
        throw new Error('Chyba při na<PERSON> zařízení')
      }
      
      const data = await response.json()
      return data.devices as DeviceWithCounts[]
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

// Get single device
export function useDevice(id: string) {
  return useQuery({
    queryKey: queryKeys.device(id),
    queryFn: () => apiGet<DeviceWithDetails>(`/api/devices/${id}`),
    enabled: !!id,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

// Get device history
export function useDeviceHistory(id: string) {
  return useQuery({
    queryKey: queryKeys.deviceHistory(id),
    queryFn: () => apiGet(`/api/devices/${id}/history`),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

// Create device mutation
export function useCreateDevice() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: DeviceFormData) => apiPost<Device>('/api/devices', data),
    onSuccess: (newDevice) => {
      // Invalidate devices list
      invalidateQueries.devices()
      
      // Optimistically add to cache
      optimisticUpdates.addDeviceToList(newDevice)
      
      // Show success message
      console.log(SUCCESS_MESSAGES.DEVICE_CREATED)
    },
    onError: (error) => {
      console.error('Chyba při vytváření zařízení:', error)
    },
  })
}

// Update device mutation
export function useUpdateDevice() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<DeviceFormData> }) =>
      apiPut<Device>(`/api/devices/${id}`, data),
    onMutate: async ({ id, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.device(id) })
      
      // Snapshot previous value
      const previousDevice = queryClient.getQueryData(queryKeys.device(id))
      
      // Optimistically update
      queryClient.setQueryData(queryKeys.device(id), (old: any) => ({
        ...old,
        ...data,
      }))
      
      optimisticUpdates.updateDeviceInList(id, data)
      
      return { previousDevice }
    },
    onError: (error, { id }, context) => {
      // Rollback on error
      if (context?.previousDevice) {
        queryClient.setQueryData(queryKeys.device(id), context.previousDevice)
      }
      console.error('Chyba při aktualizaci zařízení:', error)
    },
    onSettled: (data, error, { id }) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: queryKeys.device(id) })
      invalidateQueries.devices()
    },
    onSuccess: () => {
      console.log(SUCCESS_MESSAGES.DEVICE_UPDATED)
    },
  })
}

// Delete device mutation
export function useDeleteDevice() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (id: string) => apiDelete(`/api/devices/${id}`),
    onMutate: async (id) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.devices })
      
      // Optimistically remove from list
      optimisticUpdates.removeDeviceFromList(id)
    },
    onError: (error, id) => {
      // Refetch on error to restore state
      invalidateQueries.devices()
      console.error('Chyba při mazání zařízení:', error)
    },
    onSuccess: () => {
      invalidateQueries.devices()
      console.log(SUCCESS_MESSAGES.DEVICE_DELETED)
    },
  })
}

// Bulk operations
export function useBulkUpdateDevices() {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: ({ ids, data }: { ids: string[]; data: Partial<DeviceFormData> }) =>
      apiPost('/api/devices/bulk-update', { ids, data }),
    onSuccess: () => {
      invalidateQueries.devices()
      console.log(`Úspěšně aktualizováno zařízení`)
    },
    onError: (error) => {
      console.error('Chyba při hromadné aktualizaci:', error)
    },
  })
}

// Prefetch device details
export function usePrefetchDevice() {
  const queryClient = useQueryClient()
  
  return (id: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.device(id),
      queryFn: () => apiGet<DeviceWithDetails>(`/api/devices/${id}`),
      staleTime: 10 * 60 * 1000,
    })
  }
}
