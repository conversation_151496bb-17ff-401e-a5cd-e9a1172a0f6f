import bcrypt from 'bcryptjs'
import { prisma } from './prisma'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'

export interface User {
  id: string
  email: string
  name: string | null
  isSuperAdmin?: boolean
}

export interface UserWithOrganizations extends User {
  isSuperAdmin: boolean
  organizationMembers: Array<{
    id: string
    role: string
    organization: {
      id: string
      name: string
      slug: string
    }
  }>
}

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12)
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

export async function createSession(userId: string): Promise<string> {
  const token = generateSessionToken()
  const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days

  await prisma.session.create({
    data: {
      userId,
      token,
      expiresAt,
    },
  })

  return token
}

export async function getSessionUser(token: string): Promise<UserWithOrganizations | null> {
  const session = await prisma.session.findUnique({
    where: { token },
    include: {
      user: {
        include: {
          organizationMembers: {
            include: {
              organization: true,
            },
          },
        },
      },
    },
  })

  if (!session || session.expiresAt < new Date()) {
    if (session) {
      await prisma.session.delete({ where: { id: session.id } })
    }
    return null
  }

  return session.user
}

export async function deleteSession(token: string): Promise<void> {
  await prisma.session.deleteMany({
    where: { token },
  })
}

export async function getCurrentUser(): Promise<UserWithOrganizations | null> {
  const cookieStore = await cookies()
  const token = cookieStore.get('session')?.value

  if (!token) {
    return null
  }

  return getSessionUser(token)
}

export async function requireAuth(): Promise<UserWithOrganizations> {
  const user = await getCurrentUser()

  if (!user) {
    redirect('/auth/login')
  }

  return user
}

export async function requireAuthApi(): Promise<UserWithOrganizations> {
  const user = await getCurrentUser()

  if (!user) {
    throw new Error('Unauthorized')
  }

  return user
}

export function hasAdminAccess(user: UserWithOrganizations, organizationId?: string): boolean {
  // SuperAdmin má admin práva ve všech organizacích
  if (user.isSuperAdmin) {
    return true
  }

  // Pokud není zadána organizace, kontroluj jestli je admin v jakékoliv organizaci
  if (!organizationId) {
    return user.organizationMembers.some(member => member.role === 'admin')
  }

  // Kontroluj admin práva v konkrétní organizaci
  return user.organizationMembers.some(
    member => member.organization.id === organizationId && member.role === 'admin'
  )
}

export async function setSessionCookie(token: string): Promise<void> {
  const cookieStore = await cookies()
  cookieStore.set('session', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    path: '/',
  })
}

export async function clearSessionCookie(): Promise<void> {
  const cookieStore = await cookies()
  cookieStore.delete('session')
}

function generateSessionToken(): string {
  return Array.from(crypto.getRandomValues(new Uint8Array(32)))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('')
}

export async function createUser(email: string, password: string, name?: string) {
  const hashedPassword = await hashPassword(password)
  
  return prisma.user.create({
    data: {
      email,
      password: hashedPassword,
      name,
    },
  })
}

export async function authenticateUser(email: string, password: string): Promise<User | null> {
  const user = await prisma.user.findUnique({
    where: { email },
  })

  if (!user) {
    return null
  }

  const isValid = await verifyPassword(password, user.password)
  if (!isValid) {
    return null
  }

  return {
    id: user.id,
    email: user.email,
    name: user.name,
    isSuperAdmin: user.isSuperAdmin,
  }
}
