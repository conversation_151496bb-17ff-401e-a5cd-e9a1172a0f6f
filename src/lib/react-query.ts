import { QueryClient } from '@tanstack/react-query'
import { CACHE_TTL } from './constants'

// Create a client
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Stale time - how long data is considered fresh
      staleTime: CACHE_TTL.SHORT, // 5 minutes
      // Cache time - how long data stays in cache when not used
      gcTime: CACHE_TTL.LONG, // 1 hour (formerly cacheTime)
      // Retry failed requests
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors
        if (error?.status >= 400 && error?.status < 500) {
          return false
        }
        // Retry up to 3 times for other errors
        return failureCount < 3
      },
      // Refetch on window focus
      refetchOnWindowFocus: false,
      // Refetch on reconnect
      refetchOnReconnect: true,
    },
    mutations: {
      // Retry failed mutations
      retry: 1,
    },
  },
})

// Query keys factory
export const queryKeys = {
  // User queries
  user: ['user'] as const,
  userProfile: () => [...queryKeys.user, 'profile'] as const,
  
  // Organization queries
  organizations: ['organizations'] as const,
  organizationsList: () => [...queryKeys.organizations, 'list'] as const,
  organization: (id: string) => [...queryKeys.organizations, 'detail', id] as const,
  organizationStats: (id: string) => [...queryKeys.organizations, 'stats', id] as const,
  organizationUsers: (id: string) => [...queryKeys.organizations, 'users', id] as const,
  
  // Device queries
  devices: ['devices'] as const,
  devicesList: (filters?: Record<string, any>) => 
    [...queryKeys.devices, 'list', filters] as const,
  device: (id: string) => [...queryKeys.devices, 'detail', id] as const,
  deviceHistory: (id: string) => [...queryKeys.devices, 'history', id] as const,
  deviceFiles: (id: string) => [...queryKeys.devices, 'files', id] as const,
  
  // Stats queries
  stats: ['stats'] as const,
  dashboardStats: (orgId?: string) => [...queryKeys.stats, 'dashboard', orgId] as const,
  
  // Admin queries
  admin: ['admin'] as const,
  adminOrganizations: () => [...queryKeys.admin, 'organizations'] as const,
} as const

// Invalidation helpers
export const invalidateQueries = {
  user: () => queryClient.invalidateQueries({ queryKey: queryKeys.user }),
  organizations: () => queryClient.invalidateQueries({ queryKey: queryKeys.organizations }),
  devices: () => queryClient.invalidateQueries({ queryKey: queryKeys.devices }),
  stats: () => queryClient.invalidateQueries({ queryKey: queryKeys.stats }),
  admin: () => queryClient.invalidateQueries({ queryKey: queryKeys.admin }),
  
  // Specific invalidations
  organizationStats: (id: string) => 
    queryClient.invalidateQueries({ queryKey: queryKeys.organizationStats(id) }),
  deviceDetail: (id: string) => 
    queryClient.invalidateQueries({ queryKey: queryKeys.device(id) }),
  devicesList: () => 
    queryClient.invalidateQueries({ queryKey: queryKeys.devices }),
}

// Prefetch helpers
export const prefetchQueries = {
  organizations: async () => {
    await queryClient.prefetchQuery({
      queryKey: queryKeys.organizationsList(),
      queryFn: () => fetch('/api/organizations').then(res => res.json()),
      staleTime: CACHE_TTL.MEDIUM,
    })
  },
  
  devices: async (filters?: Record<string, any>) => {
    const url = new URL('/api/devices', window.location.origin)
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value) url.searchParams.set(key, String(value))
      })
    }
    
    await queryClient.prefetchQuery({
      queryKey: queryKeys.devicesList(filters),
      queryFn: () => fetch(url.toString()).then(res => res.json()),
      staleTime: CACHE_TTL.SHORT,
    })
  },
  
  dashboardStats: async (orgId?: string) => {
    const url = orgId ? `/api/organizations/${orgId}/stats` : '/api/stats'
    
    await queryClient.prefetchQuery({
      queryKey: queryKeys.dashboardStats(orgId),
      queryFn: () => fetch(url).then(res => res.json()),
      staleTime: CACHE_TTL.SHORT,
    })
  },
}

// Cache management
export const cacheUtils = {
  // Clear all cache
  clearAll: () => queryClient.clear(),
  
  // Remove specific queries
  removeQueries: (queryKey: readonly unknown[]) => 
    queryClient.removeQueries({ queryKey }),
  
  // Get cached data
  getQueryData: <T>(queryKey: readonly unknown[]): T | undefined => 
    queryClient.getQueryData<T>(queryKey),
  
  // Set cached data
  setQueryData: <T>(queryKey: readonly unknown[], data: T) => 
    queryClient.setQueryData<T>(queryKey, data),
  
  // Update cached data
  updateQueryData: <T>(
    queryKey: readonly unknown[], 
    updater: (oldData: T | undefined) => T
  ) => queryClient.setQueryData<T>(queryKey, updater),
}

// Optimistic updates helpers
export const optimisticUpdates = {
  // Update device in list
  updateDeviceInList: (deviceId: string, updates: Partial<any>) => {
    queryClient.setQueriesData(
      { queryKey: queryKeys.devices },
      (oldData: any) => {
        if (!oldData?.devices) return oldData
        
        return {
          ...oldData,
          devices: oldData.devices.map((device: any) =>
            device.id === deviceId ? { ...device, ...updates } : device
          )
        }
      }
    )
  },
  
  // Add device to list
  addDeviceToList: (newDevice: any) => {
    queryClient.setQueriesData(
      { queryKey: queryKeys.devices },
      (oldData: any) => {
        if (!oldData?.devices) return oldData
        
        return {
          ...oldData,
          devices: [newDevice, ...oldData.devices]
        }
      }
    )
  },
  
  // Remove device from list
  removeDeviceFromList: (deviceId: string) => {
    queryClient.setQueriesData(
      { queryKey: queryKeys.devices },
      (oldData: any) => {
        if (!oldData?.devices) return oldData
        
        return {
          ...oldData,
          devices: oldData.devices.filter((device: any) => device.id !== deviceId)
        }
      }
    )
  },
}

// Background sync
export const backgroundSync = {
  // Sync critical data in background
  syncCriticalData: async () => {
    await Promise.allSettled([
      queryClient.refetchQueries({ queryKey: queryKeys.user }),
      queryClient.refetchQueries({ queryKey: queryKeys.organizations }),
    ])
  },
  
  // Sync dashboard data
  syncDashboardData: async (orgId?: string) => {
    await Promise.allSettled([
      queryClient.refetchQueries({ queryKey: queryKeys.dashboardStats(orgId) }),
      queryClient.refetchQueries({ queryKey: queryKeys.devicesList() }),
    ])
  },
}

// Error handling for queries
export const queryErrorHandler = (error: any) => {
  console.error('Query error:', error)
  
  // Handle specific error types
  if (error?.status === 401) {
    // Redirect to login
    window.location.href = '/auth/login'
    return
  }
  
  if (error?.status === 403) {
    // Show permission error
    console.error('Permission denied')
    return
  }
  
  // Log other errors
  console.error('Unexpected query error:', error)
}
