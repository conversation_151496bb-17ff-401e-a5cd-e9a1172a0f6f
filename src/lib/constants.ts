import type { DeviceStatus, UserRole } from '@/types'

// Device constants
export const DEVICE_STATUSES = {
  AKTIVNI: 'aktivni',
  ZAPUJCENO: 'zapujceno',
  SERVIS: 'servis',
  VYRAZENO: 'vyrazeno'
} as const

export const DEVICE_STATUS_LABELS: Record<DeviceStatus, string> = {
  aktivni: 'Aktivní',
  zapujceno: 'Zapůjčeno',
  servis: 'Servis',
  vyrazeno: 'Vyřazeno'
}

export const DEVICE_STATUS_COLORS: Record<DeviceStatus, string> = {
  aktivni: 'bg-green-100 text-green-800 border-green-200',
  zapujceno: 'bg-blue-100 text-blue-800 border-blue-200',
  servis: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  vyrazeno: 'bg-red-100 text-red-800 border-red-200'
}

export const DEVICE_TYPES = [
  'laptop',
  'desktop',
  'server',
  'mobile',
  'tablet',
  'printer',
  'monitor',
  'router',
  'switch',
  'other'
] as const

export const DEVICE_TYPE_LABELS: Record<string, string> = {
  laptop: 'Notebook',
  desktop: 'Desktop',
  server: 'Server',
  mobile: 'Mobilní telefon',
  tablet: 'Tablet',
  printer: 'Tiskárna',
  monitor: 'Monitor',
  router: 'Router',
  switch: 'Switch',
  other: 'Ostatní'
}

// User constants
export const USER_ROLES = {
  ADMIN: 'admin',
  TECHNIK: 'technik',
  CTENAR: 'ctenar'
} as const

export const USER_ROLE_LABELS: Record<UserRole, string> = {
  admin: 'Administrátor',
  technik: 'Technik',
  ctenar: 'Čtenář'
}

export const USER_ROLE_DESCRIPTIONS: Record<UserRole, string> = {
  admin: 'Plný přístup ke správě organizace',
  technik: 'Správa zařízení a jejich údržba',
  ctenar: 'Pouze čtení informací o zařízeních'
}

// File upload constants
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'image/jpeg',
    'image/png',
    'image/gif'
  ],
  ALLOWED_EXTENSIONS: [
    '.pdf',
    '.doc',
    '.docx',
    '.xls',
    '.xlsx',
    '.txt',
    '.jpg',
    '.jpeg',
    '.png',
    '.gif'
  ]
} as const

export const FILE_TYPE_LABELS: Record<string, string> = {
  'application/pdf': 'PDF dokument',
  'application/msword': 'Word dokument',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word dokument',
  'application/vnd.ms-excel': 'Excel tabulka',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel tabulka',
  'text/plain': 'Textový soubor',
  'image/jpeg': 'JPEG obrázek',
  'image/png': 'PNG obrázek',
  'image/gif': 'GIF obrázek'
}

// Pagination constants
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100]
} as const

// API constants
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    ME: '/api/auth/me'
  },
  DEVICES: {
    LIST: '/api/devices',
    CREATE: '/api/devices',
    GET: (id: string) => `/api/devices/${id}`,
    UPDATE: (id: string) => `/api/devices/${id}`,
    DELETE: (id: string) => `/api/devices/${id}`,
    HISTORY: (id: string) => `/api/devices/${id}/history`
  },
  ORGANIZATIONS: {
    LIST: '/api/organizations',
    CREATE: '/api/organizations',
    GET: (id: string) => `/api/organizations/${id}`,
    UPDATE: (id: string) => `/api/organizations/${id}`,
    DELETE: (id: string) => `/api/organizations/${id}`,
    STATS: (id: string) => `/api/organizations/${id}/stats`,
    USERS: (id: string) => `/api/organizations/${id}/users`
  },
  ADMIN: {
    ORGANIZATIONS: '/api/admin/organizations'
  },
  FILES: {
    UPLOAD: '/api/files/upload',
    DOWNLOAD: (id: string) => `/api/files/${id}`,
    DELETE: (id: string) => `/api/files/${id}`
  }
} as const

// Cache constants
export const CACHE_KEYS = {
  USER: 'user',
  ORGANIZATIONS: 'organizations',
  DEVICES: 'devices',
  STATS: 'stats'
} as const

export const CACHE_TTL = {
  SHORT: 5 * 60 * 1000, // 5 minutes
  MEDIUM: 15 * 60 * 1000, // 15 minutes
  LONG: 60 * 60 * 1000 // 1 hour
} as const

// UI constants
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536
} as const

export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500
} as const

// Form constants
export const FORM_VALIDATION = {
  MIN_PASSWORD_LENGTH: 6,
  MAX_PASSWORD_LENGTH: 100,
  MAX_NAME_LENGTH: 100,
  MAX_EMAIL_LENGTH: 255,
  MAX_DESCRIPTION_LENGTH: 500,
  MAX_NOTES_LENGTH: 1000
} as const

// Date format constants
export const DATE_FORMATS = {
  SHORT: 'dd.MM.yyyy',
  LONG: 'dd.MM.yyyy HH:mm',
  ISO: 'yyyy-MM-dd',
  TIME: 'HH:mm'
} as const

// Error messages
export const ERROR_MESSAGES = {
  REQUIRED_FIELD: 'Toto pole je povinné',
  INVALID_EMAIL: 'Neplatný email',
  PASSWORD_TOO_SHORT: `Heslo musí mít alespoň ${FORM_VALIDATION.MIN_PASSWORD_LENGTH} znaků`,
  PASSWORDS_DONT_MATCH: 'Hesla se neshodují',
  UNAUTHORIZED: 'Neautorizovaný přístup',
  FORBIDDEN: 'Nedostatečná oprávnění',
  NOT_FOUND: 'Zdroj nenalezen',
  NETWORK_ERROR: 'Chyba síťového připojení',
  UNEXPECTED_ERROR: 'Došlo k neočekávané chybě'
} as const

// Success messages
export const SUCCESS_MESSAGES = {
  DEVICE_CREATED: 'Zařízení bylo úspěšně vytvořeno',
  DEVICE_UPDATED: 'Zařízení bylo úspěšně aktualizováno',
  DEVICE_DELETED: 'Zařízení bylo úspěšně smazáno',
  USER_CREATED: 'Uživatel byl úspěšně vytvořen',
  USER_UPDATED: 'Uživatel byl úspěšně aktualizován',
  USER_DELETED: 'Uživatel byl úspěšně smazán',
  ORGANIZATION_CREATED: 'Organizace byla úspěšně vytvořena',
  ORGANIZATION_UPDATED: 'Organizace byla úspěšně aktualizována',
  ORGANIZATION_DELETED: 'Organizace byla úspěšně smazána',
  FILE_UPLOADED: 'Soubor byl úspěšně nahrán',
  PROFILE_UPDATED: 'Profil byl úspěšně aktualizován',
  PASSWORD_CHANGED: 'Heslo bylo úspěšně změněno'
} as const

// Local storage keys
export const STORAGE_KEYS = {
  THEME: 'rokitbox-theme',
  SIDEBAR_COLLAPSED: 'rokitbox-sidebar-collapsed',
  DEVICE_FILTERS: 'rokitbox-device-filters',
  TABLE_PREFERENCES: 'rokitbox-table-preferences'
} as const

// Environment constants
export const ENV = {
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production',
  IS_TEST: process.env.NODE_ENV === 'test'
} as const
