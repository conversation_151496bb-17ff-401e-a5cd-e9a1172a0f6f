import { NextResponse } from 'next/server'
import type { ApiResponse } from '@/types'
import { handleApiError } from './errors'

// Success response helpers
export function createSuccessResponse<T>(
  data: T, 
  message?: string,
  statusCode: number = 200
): NextResponse {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message
  }
  
  return NextResponse.json(response, { status: statusCode })
}

export function createCreatedResponse<T>(
  data: T, 
  message?: string
): NextResponse {
  return createSuccessResponse(data, message, 201)
}

export function createNoContentResponse(): NextResponse {
  return new NextResponse(null, { status: 204 })
}

// Error response helpers
export function createErrorResponse(
  error: string, 
  statusCode: number = 500
): NextResponse {
  const response: ApiResponse = {
    success: false,
    error
  }
  
  return NextResponse.json(response, { status: statusCode })
}

export function createValidationErrorResponse(
  errors: Record<string, string>
): NextResponse {
  const response = {
    success: false,
    error: 'Chyba validace',
    errors
  }
  
  return NextResponse.json(response, { status: 400 })
}

export function createUnauthorizedResponse(
  message: string = 'Neautorizovaný přístup'
): NextResponse {
  return createErrorResponse(message, 401)
}

export function createForbiddenResponse(
  message: string = 'Nedostatečná oprávnění'
): NextResponse {
  return createErrorResponse(message, 403)
}

export function createNotFoundResponse(
  message: string = 'Zdroj nenalezen'
): NextResponse {
  return createErrorResponse(message, 404)
}

export function createConflictResponse(
  message: string = 'Konflikt dat'
): NextResponse {
  return createErrorResponse(message, 409)
}

// API handler wrapper with error handling
export function withApiHandler<T extends any[], R>(
  handler: (...args: T) => Promise<NextResponse>
) {
  return async (...args: T): Promise<NextResponse> => {
    try {
      return await handler(...args)
    } catch (error) {
      const { error: errorMessage, statusCode } = handleApiError(error)
      return createErrorResponse(errorMessage, statusCode)
    }
  }
}

// Pagination response helper
export function createPaginatedResponse<T>(
  data: T[],
  total: number,
  page: number,
  limit: number,
  message?: string
): NextResponse {
  const totalPages = Math.ceil(total / limit)
  const hasNextPage = page < totalPages
  const hasPrevPage = page > 1
  
  const response = {
    success: true,
    data,
    message,
    pagination: {
      total,
      page,
      limit,
      totalPages,
      hasNextPage,
      hasPrevPage
    }
  }
  
  return NextResponse.json(response)
}

// File response helpers
export function createFileResponse(
  buffer: Buffer,
  filename: string,
  mimeType: string
): NextResponse {
  const response = new NextResponse(buffer)
  
  response.headers.set('Content-Type', mimeType)
  response.headers.set('Content-Disposition', `attachment; filename="${filename}"`)
  
  return response
}

export function createImageResponse(
  buffer: Buffer,
  mimeType: string
): NextResponse {
  const response = new NextResponse(buffer)
  
  response.headers.set('Content-Type', mimeType)
  response.headers.set('Cache-Control', 'public, max-age=31536000, immutable')
  
  return response
}

// CORS helpers
export function addCorsHeaders(response: NextResponse): NextResponse {
  response.headers.set('Access-Control-Allow-Origin', '*')
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
  
  return response
}

export function createOptionsResponse(): NextResponse {
  const response = new NextResponse(null, { status: 200 })
  return addCorsHeaders(response)
}

// Rate limiting response
export function createRateLimitResponse(
  retryAfter: number = 60
): NextResponse {
  const response = createErrorResponse('Příliš mnoho požadavků', 429)
  response.headers.set('Retry-After', retryAfter.toString())
  
  return response
}

// Health check response
export function createHealthCheckResponse(
  status: 'healthy' | 'unhealthy' = 'healthy',
  checks?: Record<string, boolean>
): NextResponse {
  const response = {
    status,
    timestamp: new Date().toISOString(),
    checks
  }
  
  const statusCode = status === 'healthy' ? 200 : 503
  return NextResponse.json(response, { status: statusCode })
}

// Batch operation response
export function createBatchResponse<T>(
  results: Array<{ success: boolean; data?: T; error?: string }>,
  message?: string
): NextResponse {
  const successCount = results.filter(r => r.success).length
  const errorCount = results.length - successCount
  
  const response = {
    success: errorCount === 0,
    message: message || `Zpracováno ${successCount}/${results.length} položek`,
    results,
    summary: {
      total: results.length,
      success: successCount,
      errors: errorCount
    }
  }
  
  return NextResponse.json(response)
}

// Client-side API helpers
export async function handleApiResponse<T>(response: Response): Promise<T> {
  const data = await response.json()
  
  if (!response.ok) {
    throw new Error(data.error || `HTTP ${response.status}`)
  }
  
  return data.data || data
}

export async function apiGet<T>(url: string): Promise<T> {
  const response = await fetch(url)
  return handleApiResponse<T>(response)
}

export async function apiPost<T>(url: string, body: any): Promise<T> {
  const response = await fetch(url, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(body)
  })
  return handleApiResponse<T>(response)
}

export async function apiPut<T>(url: string, body: any): Promise<T> {
  const response = await fetch(url, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(body)
  })
  return handleApiResponse<T>(response)
}

export async function apiDelete<T>(url: string): Promise<T> {
  const response = await fetch(url, {
    method: 'DELETE'
  })
  return handleApiResponse<T>(response)
}
