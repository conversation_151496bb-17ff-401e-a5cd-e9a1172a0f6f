import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date) {
  return new Intl.DateTimeFormat('cs-CZ', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  }).format(new Date(date))
}

export function formatDateTime(date: string | Date) {
  return new Intl.DateTimeFormat('cs-CZ', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date))
}

export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
}

export const deviceStatuses = {
  aktivni: { label: 'Aktivní', color: 'bg-green-100 text-green-800' },
  zapujceno: { label: 'Zapůjčeno', color: 'bg-blue-100 text-blue-800' },
  servis: { label: 'V servisu', color: 'bg-yellow-100 text-yellow-800' },
  vyrazeno: { label: 'Vyřazeno', color: 'bg-red-100 text-red-800' },
} as const

export const userRoles = {
  admin: { label: 'Administrátor', color: 'bg-purple-100 text-purple-800' },
  technik: { label: 'Technik', color: 'bg-blue-100 text-blue-800' },
  ctenar: { label: 'Čtenář', color: 'bg-gray-100 text-gray-800' },
} as const
