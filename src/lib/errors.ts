import { NextResponse } from 'next/server'
import { ZodError } from 'zod'
import type { ApiResponse, ApiError } from '@/types'

// Custom error classes
export class AppError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message)
    this.name = 'AppError'
  }
}

export class ValidationError extends AppError {
  constructor(message: string, public field?: string) {
    super(message, 400, 'VALIDATION_ERROR')
    this.name = 'ValidationError'
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Neautorizovaný přístup') {
    super(message, 401, 'AUTHENTICATION_ERROR')
    this.name = 'AuthenticationError'
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Nedostatečná oprávnění') {
    super(message, 403, 'AUTHORIZATION_ERROR')
    this.name = 'AuthorizationError'
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Z<PERSON><PERSON><PERSON>') {
    super(message, 404, 'NOT_FOUND_ERROR')
    this.name = 'NotFoundError'
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Konflikt dat') {
    super(message, 409, 'CONFLICT_ERROR')
    this.name = 'ConflictError'
  }
}

export class DatabaseError extends AppError {
  constructor(message: string = 'Chyba databáze') {
    super(message, 500, 'DATABASE_ERROR')
    this.name = 'DatabaseError'
  }
}

// Error handling utilities
export function handleApiError(error: unknown): { error: string; statusCode: number } {
  console.error('API Error:', error)

  // Zod validation errors
  if (error instanceof ZodError) {
    const firstError = error.errors[0]
    return {
      error: `${firstError.path.join('.')}: ${firstError.message}`,
      statusCode: 400
    }
  }

  // Custom app errors
  if (error instanceof AppError) {
    return {
      error: error.message,
      statusCode: error.statusCode
    }
  }

  // Prisma errors
  if (error && typeof error === 'object' && 'code' in error) {
    const prismaError = error as any
    
    switch (prismaError.code) {
      case 'P2002':
        return {
          error: 'Záznam s těmito údaji již existuje',
          statusCode: 409
        }
      case 'P2025':
        return {
          error: 'Záznam nenalezen',
          statusCode: 404
        }
      case 'P2003':
        return {
          error: 'Narušení referenční integrity',
          statusCode: 400
        }
      default:
        return {
          error: 'Chyba databáze',
          statusCode: 500
        }
    }
  }

  // Network/fetch errors
  if (error instanceof TypeError && error.message.includes('fetch')) {
    return {
      error: 'Chyba síťového připojení',
      statusCode: 503
    }
  }

  // Default error
  return {
    error: 'Došlo k neočekávané chybě',
    statusCode: 500
  }
}

// API response helpers
export function createSuccessResponse<T>(data: T, message?: string): ApiResponse<T> {
  return {
    success: true,
    data,
    message
  }
}

export function createErrorResponse(error: string, statusCode: number = 500): NextResponse {
  return NextResponse.json(
    { success: false, error },
    { status: statusCode }
  )
}

export function createValidationErrorResponse(errors: Record<string, string>): NextResponse {
  return NextResponse.json(
    { 
      success: false, 
      error: 'Chyba validace',
      errors 
    },
    { status: 400 }
  )
}

// Error boundary helper
export function withErrorHandling<T extends any[], R>(
  fn: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args)
    } catch (error) {
      const { error: errorMessage, statusCode } = handleApiError(error)
      throw new AppError(errorMessage, statusCode)
    }
  }
}

// Client-side error handling
export function handleClientError(error: unknown): string {
  console.error('Client Error:', error)

  if (error instanceof Error) {
    return error.message
  }

  if (typeof error === 'string') {
    return error
  }

  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message)
  }

  return 'Došlo k neočekávané chybě'
}

// Form error handling
export function getFieldError(errors: Record<string, string>, field: string): string | undefined {
  return errors[field]
}

export function hasFieldError(errors: Record<string, string>, field: string): boolean {
  return field in errors
}

// API call wrapper with error handling
export async function apiCall<T>(
  url: string,
  options: RequestInit = {}
): Promise<T> {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    })

    const data = await response.json()

    if (!response.ok) {
      throw new AppError(
        data.error || `HTTP ${response.status}`,
        response.status
      )
    }

    return data
  } catch (error) {
    if (error instanceof AppError) {
      throw error
    }
    throw new AppError('Chyba síťového připojení', 503)
  }
}

// Retry wrapper for API calls
export async function withRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))
      
      if (i === maxRetries) {
        break
      }

      // Don't retry on client errors (4xx)
      if (error instanceof AppError && error.statusCode >= 400 && error.statusCode < 500) {
        break
      }

      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
    }
  }

  throw lastError!
}

// Error logging
export function logError(error: unknown, context?: string): void {
  const timestamp = new Date().toISOString()
  const contextStr = context ? ` [${context}]` : ''
  
  console.error(`${timestamp}${contextStr}:`, error)
  
  // In production, you might want to send errors to a logging service
  if (process.env.NODE_ENV === 'production') {
    // Send to logging service (e.g., Sentry, LogRocket, etc.)
  }
}
