import { prisma } from './prisma'

export interface UserWithPermissions {
  id: string
  email: string
  name: string | null
  isSuperAdmin: boolean
  organizationMembers: Array<{
    organizationId: string
    roleId: string | null
    legacyRole: string | null
    role?: {
      id: string
      name: string
      rolePermissions: Array<{
        permission: {
          name: string
          resource: string
          action: string
        }
      }>
    }
  }>
  userPermissions: Array<{
    permission: {
      name: string
      resource: string
      action: string
    }
    granted: boolean
  }>
}

export class PermissionService {
  /**
   * Get user with all permissions
   */
  static async getUserWithPermissions(userId: string): Promise<UserWithPermissions | null> {
    return await prisma.user.findUnique({
      where: { id: userId },
      include: {
        organizationMembers: {
          include: {
            role: {
              include: {
                rolePermissions: {
                  include: {
                    permission: true
                  }
                }
              }
            }
          }
        },
        userPermissions: {
          include: {
            permission: true
          }
        }
      }
    })
  }

  /**
   * Check if user has specific permission
   */
  static async hasPermission(
    userId: string, 
    permissionName: string, 
    organizationId?: string
  ): Promise<boolean> {
    const user = await this.getUserWithPermissions(userId)
    if (!user) return false

    // Superadmin has all permissions
    if (user.isSuperAdmin) return true

    // Check user-specific permissions (overrides role permissions)
    const userPermission = user.userPermissions.find(
      up => up.permission.name === permissionName
    )
    if (userPermission) {
      return userPermission.granted
    }

    // Check role permissions
    const relevantMemberships = organizationId 
      ? user.organizationMembers.filter(om => om.organizationId === organizationId)
      : user.organizationMembers

    for (const membership of relevantMemberships) {
      if (membership.role) {
        const hasRolePermission = membership.role.rolePermissions.some(
          rp => rp.permission.name === permissionName
        )
        if (hasRolePermission) return true
      }

      // Fallback to legacy role system
      if (membership.legacyRole) {
        const legacyPermissions = this.getLegacyPermissions(membership.legacyRole)
        if (legacyPermissions.includes(permissionName)) return true
      }
    }

    return false
  }

  /**
   * Check if user has any of the specified permissions
   */
  static async hasAnyPermission(
    userId: string, 
    permissionNames: string[], 
    organizationId?: string
  ): Promise<boolean> {
    for (const permission of permissionNames) {
      if (await this.hasPermission(userId, permission, organizationId)) {
        return true
      }
    }
    return false
  }

  /**
   * Check if user has all specified permissions
   */
  static async hasAllPermissions(
    userId: string, 
    permissionNames: string[], 
    organizationId?: string
  ): Promise<boolean> {
    for (const permission of permissionNames) {
      if (!(await this.hasPermission(userId, permission, organizationId))) {
        return false
      }
    }
    return true
  }

  /**
   * Get all permissions for user
   */
  static async getUserPermissions(userId: string, organizationId?: string): Promise<string[]> {
    const user = await this.getUserWithPermissions(userId)
    if (!user) return []

    // Superadmin has all permissions
    if (user.isSuperAdmin) {
      const allPermissions = await prisma.permission.findMany({
        select: { name: true }
      })
      return allPermissions.map(p => p.name)
    }

    const permissions = new Set<string>()

    // Add user-specific permissions
    user.userPermissions.forEach(up => {
      if (up.granted) {
        permissions.add(up.permission.name)
      } else {
        permissions.delete(up.permission.name) // Explicit deny
      }
    })

    // Add role permissions
    const relevantMemberships = organizationId 
      ? user.organizationMembers.filter(om => om.organizationId === organizationId)
      : user.organizationMembers

    for (const membership of relevantMemberships) {
      if (membership.role) {
        membership.role.rolePermissions.forEach(rp => {
          permissions.add(rp.permission.name)
        })
      }

      // Fallback to legacy role system
      if (membership.legacyRole) {
        const legacyPermissions = this.getLegacyPermissions(membership.legacyRole)
        legacyPermissions.forEach(p => permissions.add(p))
      }
    }

    return Array.from(permissions)
  }

  /**
   * Legacy role permissions mapping
   */
  private static getLegacyPermissions(role: string): string[] {
    const legacyRoleMap: Record<string, string[]> = {
      admin: [
        'devices.manage', 'users.manage', 'teams.manage', 'departments.manage',
        'audit.read', 'reports.manage', 'settings.update'
      ],
      technik: [
        'devices.create', 'devices.read', 'devices.update', 'teams.read', 'departments.read'
      ],
      ctenar: [
        'devices.read', 'teams.read', 'departments.read', 'reports.read'
      ]
    }

    return legacyRoleMap[role] || []
  }

  /**
   * Check resource-level permissions
   */
  static async canAccessResource(
    userId: string,
    resource: string,
    action: string,
    organizationId?: string
  ): Promise<boolean> {
    const permissionName = `${resource}.${action}`
    return await this.hasPermission(userId, permissionName, organizationId)
  }

  /**
   * Grant permission to user
   */
  static async grantPermission(userId: string, permissionName: string): Promise<void> {
    const permission = await prisma.permission.findUnique({
      where: { name: permissionName }
    })

    if (!permission) {
      throw new Error(`Permission ${permissionName} not found`)
    }

    await prisma.userPermission.upsert({
      where: {
        userId_permissionId: {
          userId,
          permissionId: permission.id
        }
      },
      update: { granted: true },
      create: {
        userId,
        permissionId: permission.id,
        granted: true
      }
    })
  }

  /**
   * Revoke permission from user
   */
  static async revokePermission(userId: string, permissionName: string): Promise<void> {
    const permission = await prisma.permission.findUnique({
      where: { name: permissionName }
    })

    if (!permission) {
      throw new Error(`Permission ${permissionName} not found`)
    }

    await prisma.userPermission.upsert({
      where: {
        userId_permissionId: {
          userId,
          permissionId: permission.id
        }
      },
      update: { granted: false },
      create: {
        userId,
        permissionId: permission.id,
        granted: false
      }
    })
  }
}

/**
 * Middleware for checking permissions
 */
export function requirePermission(permissionName: string, organizationId?: string) {
  return async (userId: string): Promise<boolean> => {
    return await PermissionService.hasPermission(userId, permissionName, organizationId)
  }
}

/**
 * Permission constants
 */
export const PERMISSIONS = {
  DEVICES: {
    CREATE: 'devices.create',
    READ: 'devices.read',
    UPDATE: 'devices.update',
    DELETE: 'devices.delete',
    MANAGE: 'devices.manage',
  },
  USERS: {
    CREATE: 'users.create',
    READ: 'users.read',
    UPDATE: 'users.update',
    DELETE: 'users.delete',
    MANAGE: 'users.manage',
  },
  ORGANIZATIONS: {
    CREATE: 'organizations.create',
    READ: 'organizations.read',
    UPDATE: 'organizations.update',
    DELETE: 'organizations.delete',
    MANAGE: 'organizations.manage',
  },
  TEAMS: {
    CREATE: 'teams.create',
    READ: 'teams.read',
    UPDATE: 'teams.update',
    DELETE: 'teams.delete',
    MANAGE: 'teams.manage',
  },
  DEPARTMENTS: {
    CREATE: 'departments.create',
    READ: 'departments.read',
    UPDATE: 'departments.update',
    DELETE: 'departments.delete',
    MANAGE: 'departments.manage',
  },
  AUDIT: {
    READ: 'audit.read',
    MANAGE: 'audit.manage',
  },
  REPORTS: {
    READ: 'reports.read',
    CREATE: 'reports.create',
    MANAGE: 'reports.manage',
  },
  SETTINGS: {
    READ: 'settings.read',
    UPDATE: 'settings.update',
    MANAGE: 'settings.manage',
  },
} as const
