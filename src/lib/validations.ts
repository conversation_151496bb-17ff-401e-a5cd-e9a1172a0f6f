import { z } from 'zod'
import type { DeviceStatus, UserRole } from '@/types'

// Device validation schema
export const deviceSchema = z.object({
  evidenceNumber: z.string().min(1, 'Evidenčn<PERSON> číslo je povinné').max(50, '<PERSON>viden<PERSON><PERSON><PERSON> číslo je pří<PERSON> dlouh<PERSON>'),
  name: z.string().min(1, 'N<PERSON>zev je povinný').max(100, 'Název je příli<PERSON> dlouh<PERSON>'),
  description: z.string().max(500, 'Popis je příli<PERSON> dlouhý').optional(),
  deviceType: z.string().min(1, 'Typ zařízení je povinný').max(50, 'Typ zařízení je příli<PERSON> dlouh<PERSON>'),
  manufacturer: z.string().min(1, 'Výrobce je povinný').max(50, '<PERSON><PERSON><PERSON><PERSON><PERSON> je př<PERSON><PERSON> dlouh<PERSON>'),
  model: z.string().min(1, 'Model je povinný').max(50, 'Model je př<PERSON><PERSON> dlouh<PERSON>'),
  serialNumber: z.string().max(100, 'S<PERSON>riov<PERSON> číslo je příliš dlouhé').optional(),
  location: z.string().min(1, 'Umístění je povinné').max(100, 'Umístění je příliš dlouhé'),
  status: z.enum(['aktivni', 'zapujceno', 'servis', 'vyrazeno'] as const),
  purchaseDate: z.string().optional().refine((date) => {
    if (!date) return true
    return !isNaN(Date.parse(date))
  }, 'Neplatné datum nákupu'),
  warrantyExpiry: z.string().optional().refine((date) => {
    if (!date) return true
    return !isNaN(Date.parse(date))
  }, 'Neplatné datum konce záruky'),
  notes: z.string().max(1000, 'Poznámky jsou příliš dlouhé').optional(),
  organizationId: z.string().min(1, 'Organizace je povinná'),
})

export type DeviceFormData = z.infer<typeof deviceSchema>

// User validation schema
export const userSchema = z.object({
  name: z.string().min(1, 'Jméno je povinné').max(100, 'Jméno je příliš dlouhé'),
  email: z.string().email('Neplatný email').max(255, 'Email je příliš dlouhý'),
  password: z.string().min(6, 'Heslo musí mít alespoň 6 znaků').max(100, 'Heslo je příliš dlouhé').optional(),
  role: z.enum(['admin', 'technik', 'ctenar'] as const),
})

export type UserFormData = z.infer<typeof userSchema>

// Registration validation schema
export const registrationSchema = z.object({
  name: z.string().min(1, 'Jméno je povinné').max(100, 'Jméno je příliš dlouhé'),
  email: z.string().email('Neplatný email').max(255, 'Email je příliš dlouhý'),
  password: z.string().min(6, 'Heslo musí mít alespoň 6 znaků').max(100, 'Heslo je příliš dlouhé'),
  confirmPassword: z.string(),
  organizationName: z.string().min(1, 'Název organizace je povinný').max(100, 'Název organizace je příliš dlouhý'),
  organizationSlug: z.string().min(1, 'Slug organizace je povinný').max(50, 'Slug organizace je příliš dlouhý')
    .regex(/^[a-z0-9-]+$/, 'Slug může obsahovat pouze malá písmena, čísla a pomlčky'),
}).refine((data) => data.password === data.confirmPassword, {
  message: 'Hesla se neshodují',
  path: ['confirmPassword'],
})

export type RegistrationFormData = z.infer<typeof registrationSchema>

// Login validation schema
export const loginSchema = z.object({
  email: z.string().email('Neplatný email').max(255, 'Email je příliš dlouhý'),
  password: z.string().min(1, 'Heslo je povinné').max(100, 'Heslo je příliš dlouhé'),
})

export type LoginFormData = z.infer<typeof loginSchema>

// Organization validation schema
export const organizationSchema = z.object({
  name: z.string().min(1, 'Název je povinný').max(100, 'Název je příliš dlouhý'),
  slug: z.string().min(1, 'Slug je povinný').max(50, 'Slug je příliš dlouhý')
    .regex(/^[a-z0-9-]+$/, 'Slug může obsahovat pouze malá písmena, čísla a pomlčky'),
  description: z.string().max(500, 'Popis je příliš dlouhý').optional(),
  adminEmail: z.string().email('Neplatný email').max(255, 'Email je příliš dlouhý').optional(),
})

export type OrganizationFormData = z.infer<typeof organizationSchema>

// Profile update validation schema
export const profileSchema = z.object({
  name: z.string().min(1, 'Jméno je povinné').max(100, 'Jméno je příliš dlouhé'),
  email: z.string().email('Neplatný email').max(255, 'Email je příliš dlouhý'),
})

export type ProfileFormData = z.infer<typeof profileSchema>

// Password change validation schema
export const passwordChangeSchema = z.object({
  currentPassword: z.string().min(1, 'Současné heslo je povinné'),
  newPassword: z.string().min(6, 'Nové heslo musí mít alespoň 6 znaků').max(100, 'Heslo je příliš dlouhé'),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Hesla se neshodují',
  path: ['confirmPassword'],
})

export type PasswordChangeFormData = z.infer<typeof passwordChangeSchema>

// File upload validation schema
export const fileUploadSchema = z.object({
  deviceId: z.string().min(1, 'ID zařízení je povinné'),
  file: z.instanceof(File).refine((file) => {
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif'
    ]
    return allowedTypes.includes(file.type)
  }, 'Nepodporovaný typ souboru').refine((file) => {
    const maxSize = 10 * 1024 * 1024 // 10MB
    return file.size <= maxSize
  }, 'Soubor je příliš velký (max. 10MB)'),
})

export type FileUploadFormData = z.infer<typeof fileUploadSchema>

// Search validation schema
export const searchSchema = z.object({
  q: z.string().max(100, 'Vyhledávací dotaz je příliš dlouhý').optional(),
  status: z.enum(['all', 'aktivni', 'zapujceno', 'servis', 'vyrazeno'] as const).optional(),
  location: z.string().max(100, 'Umístění je příliš dlouhé').optional(),
  type: z.string().max(50, 'Typ je příliš dlouhý').optional(),
  org: z.string().optional(),
  page: z.string().regex(/^\d+$/, 'Stránka musí být číslo').optional(),
  limit: z.string().regex(/^\d+$/, 'Limit musí být číslo').optional(),
})

export type SearchFormData = z.infer<typeof searchSchema>

// Validation helper functions
export function validateDeviceData(data: unknown): DeviceFormData {
  return deviceSchema.parse(data)
}

export function validateUserData(data: unknown): UserFormData {
  return userSchema.parse(data)
}

export function validateRegistrationData(data: unknown): RegistrationFormData {
  return registrationSchema.parse(data)
}

export function validateLoginData(data: unknown): LoginFormData {
  return loginSchema.parse(data)
}

export function validateOrganizationData(data: unknown): OrganizationFormData {
  return organizationSchema.parse(data)
}

export function validateProfileData(data: unknown): ProfileFormData {
  return profileSchema.parse(data)
}

export function validatePasswordChangeData(data: unknown): PasswordChangeFormData {
  return passwordChangeSchema.parse(data)
}

export function validateSearchData(data: unknown): SearchFormData {
  return searchSchema.parse(data)
}
