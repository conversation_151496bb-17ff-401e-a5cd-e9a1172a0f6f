import { prisma } from './prisma'
import { NextRequest } from 'next/server'

export interface AuditLogEntry {
  userId?: string
  action: string
  resource: string
  resourceId?: string
  details?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  organizationId?: string
}

export class AuditLogger {
  /**
   * Log an audit event
   */
  static async log(entry: AuditLogEntry): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          userId: entry.userId,
          action: entry.action,
          resource: entry.resource,
          resourceId: entry.resourceId,
          details: entry.details ? JSON.stringify(entry.details) : null,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent,
          organizationId: entry.organizationId,
        }
      })
    } catch (error) {
      console.error('Failed to log audit event:', error)
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Log from Next.js request
   */
  static async logFromRequest(
    request: NextRequest,
    entry: Omit<AuditLogEntry, 'ipAddress' | 'userAgent'>
  ): Promise<void> {
    const ipAddress = this.getClientIP(request)
    const userAgent = request.headers.get('user-agent') || undefined

    await this.log({
      ...entry,
      ipAddress,
      userAgent,
    })
  }

  /**
   * Get client IP from request
   */
  private static getClientIP(request: NextRequest): string | undefined {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIP = request.headers.get('x-real-ip')
    const remoteAddr = request.headers.get('remote-addr')

    if (forwarded) {
      return forwarded.split(',')[0].trim()
    }
    if (realIP) {
      return realIP
    }
    if (remoteAddr) {
      return remoteAddr
    }

    return undefined
  }

  /**
   * Log user authentication events
   */
  static async logAuth(
    action: 'LOGIN' | 'LOGOUT' | 'LOGIN_FAILED' | 'REGISTER',
    userId?: string,
    details?: Record<string, any>,
    request?: NextRequest
  ): Promise<void> {
    const entry: AuditLogEntry = {
      userId,
      action,
      resource: 'auth',
      details,
    }

    if (request) {
      await this.logFromRequest(request, entry)
    } else {
      await this.log(entry)
    }
  }

  /**
   * Log device operations
   */
  static async logDevice(
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'VIEW',
    deviceId: string,
    userId?: string,
    details?: Record<string, any>,
    organizationId?: string,
    request?: NextRequest
  ): Promise<void> {
    const entry: AuditLogEntry = {
      userId,
      action,
      resource: 'device',
      resourceId: deviceId,
      details,
      organizationId,
    }

    if (request) {
      await this.logFromRequest(request, entry)
    } else {
      await this.log(entry)
    }
  }

  /**
   * Log user operations
   */
  static async logUser(
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'VIEW' | 'ROLE_CHANGE',
    targetUserId: string,
    performedBy?: string,
    details?: Record<string, any>,
    organizationId?: string,
    request?: NextRequest
  ): Promise<void> {
    const entry: AuditLogEntry = {
      userId: performedBy,
      action,
      resource: 'user',
      resourceId: targetUserId,
      details,
      organizationId,
    }

    if (request) {
      await this.logFromRequest(request, entry)
    } else {
      await this.log(entry)
    }
  }

  /**
   * Log organization operations
   */
  static async logOrganization(
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'VIEW',
    organizationId: string,
    userId?: string,
    details?: Record<string, any>,
    request?: NextRequest
  ): Promise<void> {
    const entry: AuditLogEntry = {
      userId,
      action,
      resource: 'organization',
      resourceId: organizationId,
      details,
      organizationId,
    }

    if (request) {
      await this.logFromRequest(request, entry)
    } else {
      await this.log(entry)
    }
  }

  /**
   * Log team operations
   */
  static async logTeam(
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'VIEW' | 'MEMBER_ADD' | 'MEMBER_REMOVE',
    teamId: string,
    userId?: string,
    details?: Record<string, any>,
    organizationId?: string,
    request?: NextRequest
  ): Promise<void> {
    const entry: AuditLogEntry = {
      userId,
      action,
      resource: 'team',
      resourceId: teamId,
      details,
      organizationId,
    }

    if (request) {
      await this.logFromRequest(request, entry)
    } else {
      await this.log(entry)
    }
  }

  /**
   * Log department operations
   */
  static async logDepartment(
    action: 'CREATE' | 'UPDATE' | 'DELETE' | 'VIEW',
    departmentId: string,
    userId?: string,
    details?: Record<string, any>,
    organizationId?: string,
    request?: NextRequest
  ): Promise<void> {
    const entry: AuditLogEntry = {
      userId,
      action,
      resource: 'department',
      resourceId: departmentId,
      details,
      organizationId,
    }

    if (request) {
      await this.logFromRequest(request, entry)
    } else {
      await this.log(entry)
    }
  }

  /**
   * Log permission changes
   */
  static async logPermission(
    action: 'GRANT' | 'REVOKE' | 'ROLE_ASSIGN' | 'ROLE_REMOVE',
    targetUserId: string,
    performedBy?: string,
    details?: Record<string, any>,
    organizationId?: string,
    request?: NextRequest
  ): Promise<void> {
    const entry: AuditLogEntry = {
      userId: performedBy,
      action,
      resource: 'permission',
      resourceId: targetUserId,
      details,
      organizationId,
    }

    if (request) {
      await this.logFromRequest(request, entry)
    } else {
      await this.log(entry)
    }
  }

  /**
   * Get audit logs with filtering
   */
  static async getLogs(options: {
    userId?: string
    resource?: string
    action?: string
    organizationId?: string
    startDate?: Date
    endDate?: Date
    limit?: number
    offset?: number
  }) {
    const where: any = {}

    if (options.userId) where.userId = options.userId
    if (options.resource) where.resource = options.resource
    if (options.action) where.action = options.action
    if (options.organizationId) where.organizationId = options.organizationId

    if (options.startDate || options.endDate) {
      where.createdAt = {}
      if (options.startDate) where.createdAt.gte = options.startDate
      if (options.endDate) where.createdAt.lte = options.endDate
    }

    const [logs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: options.limit || 50,
        skip: options.offset || 0,
      }),
      prisma.auditLog.count({ where })
    ])

    return {
      logs: logs.map(log => ({
        ...log,
        details: log.details ? JSON.parse(log.details) : null,
      })),
      total,
      hasMore: (options.offset || 0) + logs.length < total,
    }
  }

  /**
   * Get audit statistics
   */
  static async getStats(organizationId?: string) {
    const where = organizationId ? { organizationId } : {}

    const [
      totalLogs,
      todayLogs,
      weekLogs,
      topUsers,
      topResources,
      topActions
    ] = await Promise.all([
      prisma.auditLog.count({ where }),
      prisma.auditLog.count({
        where: {
          ...where,
          createdAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          }
        }
      }),
      prisma.auditLog.count({
        where: {
          ...where,
          createdAt: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
          }
        }
      }),
      prisma.auditLog.groupBy({
        by: ['userId'],
        where: {
          ...where,
          userId: { not: null }
        },
        _count: true,
        orderBy: {
          _count: {
            userId: 'desc'
          }
        },
        take: 5
      }),
      prisma.auditLog.groupBy({
        by: ['resource'],
        where,
        _count: true,
        orderBy: {
          _count: {
            resource: 'desc'
          }
        },
        take: 5
      }),
      prisma.auditLog.groupBy({
        by: ['action'],
        where,
        _count: true,
        orderBy: {
          _count: {
            action: 'desc'
          }
        },
        take: 5
      })
    ])

    return {
      totalLogs,
      todayLogs,
      weekLogs,
      topUsers,
      topResources,
      topActions,
    }
  }
}
