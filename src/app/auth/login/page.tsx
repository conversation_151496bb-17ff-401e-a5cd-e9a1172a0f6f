'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Monitor, Shield, Zap } from 'lucide-react'

export default function LoginPage() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const router = useRouter()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    console.log('🔐 Attempting login with:', { email, password: '***' })

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      console.log('📡 Response status:', response.status)
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()))

      const data = await response.json()
      console.log('📡 Response data:', data)

      if (response.ok) {
        console.log('✅ Login successful, redirecting to dashboard...')
        router.push('/dashboard')
        router.refresh()
      } else {
        console.log('❌ Login failed:', data.error)
        setError(data.error || 'Chyba při přihlašování')
      }
    } catch (err) {
      console.error('❌ Login error:', err)
      setError('Došlo k neočekávané chybě')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-orange-50 to-gray-100">
      <div className="flex min-h-screen">
        {/* Left side - Branding */}
        <div className="hidden lg:flex lg:w-1/2 gradient-gray p-12 text-white relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-transparent"></div>
          <div className="relative z-10 flex flex-col justify-center max-w-md">
            <div className="mb-8">
              <div className="flex items-center space-x-3 mb-6">
                <div className="p-3 bg-orange-500/20 rounded-2xl backdrop-blur-sm border border-orange-400/30">
                  <Monitor className="h-8 w-8 text-orange-300" />
                </div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-white to-orange-200 bg-clip-text text-transparent">
                  RokitBox
                </h1>
              </div>
              <p className="text-xl text-gray-200 mb-8 leading-relaxed">
                Moderní evidenční systém hardwaru pro vaši organizaci
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div className="p-2 bg-orange-500/20 rounded-xl backdrop-blur-sm border border-orange-400/30">
                  <Shield className="h-5 w-5 text-orange-300" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1 text-white">Bezpečné a lokální</h3>
                  <p className="text-gray-300 text-sm">Vaše data zůstávají na vašem serveru</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div className="p-2 bg-orange-500/20 rounded-xl backdrop-blur-sm border border-orange-400/30">
                  <Zap className="h-5 w-5 text-orange-300" />
                </div>
                <div>
                  <h3 className="font-semibold mb-1 text-white">Rychlé a efektivní</h3>
                  <p className="text-gray-300 text-sm">Moderní rozhraní pro snadnou správu</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Login form */}
        <div className="flex-1 flex items-center justify-center p-8">
          <Card className="w-full max-w-md shadow-orange-lg border-0 bg-white/80 backdrop-blur-sm">
            <CardHeader className="space-y-1 text-center pb-8">
              <div className="lg:hidden flex items-center justify-center space-x-2 mb-4">
                <div className="p-2 bg-orange-100 rounded-xl">
                  <Monitor className="h-8 w-8 text-orange-600" />
                </div>
                <h1 className="text-2xl font-bold text-gradient-orange">RokitBox</h1>
              </div>
              <CardTitle className="text-2xl font-bold text-gray-900">
                Vítejte zpět
              </CardTitle>
              <CardDescription className="text-gray-600">
                Přihlaste se do svého účtu a pokračujte v práci
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              <form onSubmit={handleLogin} className="space-y-4">
                {error && (
                  <Alert variant="destructive" className="border-red-200 bg-red-50">
                    <AlertDescription className="text-red-800">{error}</AlertDescription>
                  </Alert>
                )}

                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                    E-mailová adresa
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="password" className="text-sm font-medium text-gray-700">
                    Heslo
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="••••••••"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full h-12 gradient-orange hover:shadow-orange text-white font-medium rounded-xl transition-all duration-200 transform hover:scale-[1.02]"
                  disabled={loading}
                >
                  {loading ? 'Přihlašuji...' : 'Přihlásit se'}
                </Button>
              </form>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">nebo</span>
                </div>
              </div>

              <div className="text-center">
                <span className="text-sm text-gray-600">Nemáte účet? </span>
                <Link
                  href="/auth/register"
                  className="text-sm font-medium text-orange-600 hover:text-orange-500 transition-colors duration-200"
                >
                  Zaregistrujte se zdarma
                </Link>
              </div>

              {/* Debug informace */}
              <div className="mt-6 p-4 bg-gray-50 rounded-lg border">
                <h4 className="text-sm font-medium text-gray-700 mb-2">🔧 Testovací přihlašovací údaje:</h4>
                <div className="text-xs text-gray-600 space-y-1">
                  <div><strong>Email:</strong> <EMAIL></div>
                  <div><strong>Heslo:</strong> admin123</div>
                  <div className="mt-2 text-orange-600">
                    Otevřete Developer Tools (F12) pro debug informace
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
