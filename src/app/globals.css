@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 210 11% 15%;
    --card: 0 0% 100%;
    --card-foreground: 210 11% 15%;
    --popover: 0 0% 100%;
    --popover-foreground: 210 11% 15%;
    --primary: 24 100% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 11% 96%;
    --secondary-foreground: 210 11% 15%;
    --muted: 210 11% 96%;
    --muted-foreground: 210 6% 46%;
    --accent: 24 100% 95%;
    --accent-foreground: 24 100% 20%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 210 11% 91%;
    --input: 210 11% 91%;
    --ring: 24 100% 50%;
    --radius: 0.75rem;

    /* Custom orange and gray palette */
    --orange-50: 24 100% 97%;
    --orange-100: 24 100% 95%;
    --orange-200: 24 100% 90%;
    --orange-300: 24 100% 80%;
    --orange-400: 24 100% 65%;
    --orange-500: 24 100% 50%;
    --orange-600: 24 100% 45%;
    --orange-700: 24 100% 35%;
    --orange-800: 24 100% 25%;
    --orange-900: 24 100% 15%;

    --gray-50: 210 11% 98%;
    --gray-100: 210 11% 96%;
    --gray-200: 210 11% 91%;
    --gray-300: 210 11% 83%;
    --gray-400: 210 6% 63%;
    --gray-500: 210 6% 46%;
    --gray-600: 210 11% 35%;
    --gray-700: 210 11% 25%;
    --gray-800: 210 11% 15%;
    --gray-900: 210 11% 9%;
  }

  .dark {
    --background: 210 11% 9%;
    --foreground: 210 11% 98%;
    --card: 210 11% 15%;
    --card-foreground: 210 11% 98%;
    --popover: 210 11% 15%;
    --popover-foreground: 210 11% 98%;
    --primary: 24 100% 50%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 11% 25%;
    --secondary-foreground: 210 11% 98%;
    --muted: 210 11% 25%;
    --muted-foreground: 210 6% 63%;
    --accent: 24 100% 15%;
    --accent-foreground: 24 100% 90%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 210 11% 25%;
    --input: 210 11% 25%;
    --ring: 24 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
}

@layer utilities {
  .gradient-orange {
    background: linear-gradient(135deg, hsl(var(--orange-500)) 0%, hsl(var(--orange-600)) 100%);
  }

  .gradient-orange-light {
    background: linear-gradient(135deg, hsl(var(--orange-50)) 0%, hsl(var(--orange-100)) 100%);
  }

  .gradient-gray {
    background: linear-gradient(135deg, hsl(var(--gray-800)) 0%, hsl(var(--gray-900)) 100%);
  }

  .text-gradient-orange {
    background: linear-gradient(135deg, hsl(var(--orange-500)) 0%, hsl(var(--orange-600)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .shadow-orange {
    box-shadow: 0 4px 14px 0 rgba(255, 102, 0, 0.15);
  }

  .shadow-orange-lg {
    box-shadow: 0 10px 25px -3px rgba(255, 102, 0, 0.1), 0 4px 6px -2px rgba(255, 102, 0, 0.05);
  }
}
