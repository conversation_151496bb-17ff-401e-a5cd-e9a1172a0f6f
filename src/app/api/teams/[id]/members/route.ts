import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from '@/lib/auth'
import { PermissionService, PERMISSIONS } from '@/lib/permissions'
import { AuditLogger } from '@/lib/audit'
import { z } from 'zod'

const addMemberSchema = z.object({
  userId: z.string().min(1, 'User ID je povinný'),
  role: z.string().optional().default('member'),
})

const updateMemberSchema = z.object({
  role: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const team = await prisma.team.findUnique({
      where: { id: params.id },
      select: { organizationId: true },
    })

    if (!team) {
      return NextResponse.json({ error: 'Team not found' }, { status: 404 })
    }

    // Check permissions
    const canRead = await PermissionService.hasPermission(
      session.user.id,
      PERMISSIONS.TEAMS.READ,
      team.organizationId
    )

    if (!canRead) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const members = await prisma.teamMember.findMany({
      where: { teamId: params.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      },
      orderBy: {
        joinedAt: 'asc',
      },
    })

    return NextResponse.json(members)
  } catch (error) {
    console.error('Error fetching team members:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()

    const team = await prisma.team.findUnique({
      where: { id: params.id },
      select: { organizationId: true, name: true },
    })

    if (!team) {
      return NextResponse.json({ error: 'Team not found' }, { status: 404 })
    }

    // Check permissions
    const canUpdate = await PermissionService.hasPermission(
      session.user.id,
      PERMISSIONS.TEAMS.UPDATE,
      team.organizationId
    )

    if (!canUpdate) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Validate input
    const validatedData = addMemberSchema.parse(body)

    // Verify user exists and belongs to organization
    const organizationMember = await prisma.organizationMember.findFirst({
      where: {
        userId: validatedData.userId,
        organizationId: team.organizationId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    })

    if (!organizationMember) {
      return NextResponse.json(
        { error: 'Uživatel nepatří do této organizace' },
        { status: 400 }
      )
    }

    // Check if user is already a member
    const existingMember = await prisma.teamMember.findFirst({
      where: {
        teamId: params.id,
        userId: validatedData.userId,
      },
    })

    if (existingMember) {
      return NextResponse.json(
        { error: 'Uživatel je již členem tohoto týmu' },
        { status: 400 }
      )
    }

    const teamMember = await prisma.teamMember.create({
      data: {
        teamId: params.id,
        userId: validatedData.userId,
        role: validatedData.role,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      },
    })

    // Log audit event
    await AuditLogger.logTeam(
      'MEMBER_ADD',
      params.id,
      session.user.id,
      { 
        teamName: team.name,
        addedUser: organizationMember.user.name || organizationMember.user.email,
        role: validatedData.role 
      },
      team.organizationId,
      request
    )

    return NextResponse.json(teamMember, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error adding team member:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
