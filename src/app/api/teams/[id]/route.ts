import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { PermissionService } from '@/lib/permissions'
import { AuditLogger } from '@/lib/audit'
import { z } from 'zod'

const updateTeamSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  departmentId: z.string().nullable().optional(),
  leaderId: z.string().nullable().optional(),
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const organizationId = searchParams.get('organizationId')
    const teamId = params.id

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 })
    }

    // Get current user from session
    const userResponse = await fetch(`${request.nextUrl.origin}/api/auth/me`, {
      headers: { cookie: request.headers.get('cookie') || '' }
    })
    
    if (!userResponse.ok) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { user: currentUser } = await userResponse.json()

    // Check permission
    const hasPermission = await PermissionService.hasPermission(
      currentUser.id,
      'teams.update',
      organizationId
    )

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = updateTeamSchema.parse(body)

    // Check if team exists and belongs to organization
    const existingTeam = await prisma.team.findFirst({
      where: {
        id: teamId,
        organizationId: organizationId
      }
    })

    if (!existingTeam) {
      return NextResponse.json({ error: 'Team not found' }, { status: 404 })
    }

    // Validate department if provided
    if (validatedData.departmentId) {
      const department = await prisma.department.findFirst({
        where: {
          id: validatedData.departmentId,
          organizationId: organizationId
        }
      })

      if (!department) {
        return NextResponse.json({ error: 'Department not found' }, { status: 404 })
      }
    }

    // Validate leader if provided
    if (validatedData.leaderId) {
      const leader = await prisma.user.findFirst({
        where: {
          id: validatedData.leaderId,
          organizationMembers: {
            some: {
              organizationId: organizationId
            }
          }
        }
      })

      if (!leader) {
        return NextResponse.json({ error: 'Leader not found in organization' }, { status: 404 })
      }
    }

    // Update team
    const team = await prisma.team.update({
      where: { id: teamId },
      data: validatedData,
      include: {
        department: true,
        leader: true,
        members: {
          include: {
            user: true
          }
        },
        _count: {
          select: {
            members: true
          }
        }
      }
    })

    // If leader changed, ensure leader is a member of the team
    if (validatedData.leaderId && validatedData.leaderId !== existingTeam.leaderId) {
      const existingMembership = await prisma.teamMember.findFirst({
        where: {
          teamId: teamId,
          userId: validatedData.leaderId
        }
      })

      if (!existingMembership) {
        await prisma.teamMember.create({
          data: {
            teamId: teamId,
            userId: validatedData.leaderId
          }
        })
      }
    }

    await AuditLogger.logFromRequest(request, {
      userId: currentUser.id,
      action: 'UPDATE',
      resource: 'team',
      resourceId: teamId,
      organizationId,
      details: { 
        name: team.name,
        changes: validatedData
      }
    })

    return NextResponse.json(team)
  } catch (error) {
    console.error('Error updating team:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input data', details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const organizationId = searchParams.get('organizationId')
    const teamId = params.id

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 })
    }

    // Get current user from session
    const userResponse = await fetch(`${request.nextUrl.origin}/api/auth/me`, {
      headers: { cookie: request.headers.get('cookie') || '' }
    })
    
    if (!userResponse.ok) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { user: currentUser } = await userResponse.json()

    // Check permission
    const hasPermission = await PermissionService.hasPermission(
      currentUser.id,
      'teams.delete',
      organizationId
    )

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Check if team exists and belongs to organization
    const existingTeam = await prisma.team.findFirst({
      where: {
        id: teamId,
        organizationId: organizationId
      }
    })

    if (!existingTeam) {
      return NextResponse.json({ error: 'Team not found' }, { status: 404 })
    }

    // Delete team (this will cascade delete team members)
    await prisma.team.delete({
      where: { id: teamId }
    })

    await AuditLogger.logFromRequest(request, {
      userId: currentUser.id,
      action: 'DELETE',
      resource: 'team',
      resourceId: teamId,
      organizationId,
      details: { 
        name: existingTeam.name
      }
    })

    return NextResponse.json({ message: 'Team deleted successfully' })
  } catch (error) {
    console.error('Error deleting team:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
