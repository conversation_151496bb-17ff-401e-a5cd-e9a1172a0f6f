import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from '@/lib/auth'
import { PermissionService, PERMISSIONS } from '@/lib/permissions'
import { AuditLogger } from '@/lib/audit'
import { z } from 'zod'

const createTeamSchema = z.object({
  name: z.string().min(1, 'Název je povinný'),
  description: z.string().optional(),
  departmentId: z.string().optional(),
  leaderId: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const organizationId = searchParams.get('organizationId')
    const departmentId = searchParams.get('departmentId')

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 })
    }

    // Check permissions
    const canRead = await PermissionService.hasPermission(
      session.user.id,
      PERMISSIONS.TEAMS.READ,
      organizationId
    )

    if (!canRead) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const where: any = {
      organizationId,
    }

    if (departmentId) {
      where.departmentId = departmentId
    }

    const teams = await prisma.team.findMany({
      where,
      include: {
        department: {
          select: {
            id: true,
            name: true,
          }
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              }
            }
          }
        },
        _count: {
          select: {
            members: true,
          }
        }
      },
      orderBy: {
        name: 'asc',
      },
    })

    // Log audit event
    await AuditLogger.logTeam(
      'VIEW',
      'list',
      session.user.id,
      { count: teams.length, departmentId },
      organizationId,
      request
    )

    return NextResponse.json(teams)
  } catch (error) {
    console.error('Error fetching teams:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { searchParams } = new URL(request.url)
    const organizationId = searchParams.get('organizationId')

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 })
    }

    // Check permissions
    const canCreate = await PermissionService.hasPermission(
      session.user.id,
      PERMISSIONS.TEAMS.CREATE,
      organizationId
    )

    if (!canCreate) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Validate input
    const validatedData = createTeamSchema.parse(body)

    // Check if team name already exists in organization
    const existingTeam = await prisma.team.findFirst({
      where: {
        name: validatedData.name,
        organizationId,
      },
    })

    if (existingTeam) {
      return NextResponse.json(
        { error: 'Tým s tímto názvem již existuje' },
        { status: 400 }
      )
    }

    // Verify department belongs to organization if provided
    if (validatedData.departmentId) {
      const department = await prisma.department.findFirst({
        where: {
          id: validatedData.departmentId,
          organizationId,
        },
      })

      if (!department) {
        return NextResponse.json(
          { error: 'Oddělení nepatří do této organizace' },
          { status: 400 }
        )
      }
    }

    // Verify leader exists and belongs to organization if provided
    if (validatedData.leaderId) {
      const leader = await prisma.organizationMember.findFirst({
        where: {
          userId: validatedData.leaderId,
          organizationId,
        },
      })

      if (!leader) {
        return NextResponse.json(
          { error: 'Vedoucí nepatří do této organizace' },
          { status: 400 }
        )
      }
    }

    const team = await prisma.team.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        organizationId,
        departmentId: validatedData.departmentId,
        leaderId: validatedData.leaderId,
      },
      include: {
        department: {
          select: {
            id: true,
            name: true,
          }
        },
        members: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              }
            }
          }
        },
        _count: {
          select: {
            members: true,
          }
        }
      },
    })

    // Add leader as team member if specified
    if (validatedData.leaderId) {
      await prisma.teamMember.create({
        data: {
          teamId: team.id,
          userId: validatedData.leaderId,
          role: 'lead',
        },
      })

      // Refresh team data to include the new member
      const updatedTeam = await prisma.team.findUnique({
        where: { id: team.id },
        include: {
          department: {
            select: {
              id: true,
              name: true,
            }
          },
          members: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                }
              }
            }
          },
          _count: {
            select: {
              members: true,
            }
          }
        },
      })

      // Log audit event
      await AuditLogger.logTeam(
        'CREATE',
        team.id,
        session.user.id,
        { name: team.name, departmentId: team.departmentId },
        organizationId,
        request
      )

      return NextResponse.json(updatedTeam, { status: 201 })
    }

    // Log audit event
    await AuditLogger.logTeam(
      'CREATE',
      team.id,
      session.user.id,
      { name: team.name, departmentId: team.departmentId },
      organizationId,
      request
    )

    return NextResponse.json(team, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating team:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
