import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth()
    
    // Get organization filter
    const organizationId = user.isSuperAdmin 
      ? request.nextUrl.searchParams.get('organizationId') || undefined
      : user.organizationMembers[0]?.organizationId

    if (!organizationId && !user.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    const whereClause = user.isSuperAdmin && !organizationId 
      ? {} 
      : { organizationId }

    // Get basic counts
    const [
      totalDevices,
      activeDevices,
      borrowedDevices,
      serviceDevices,
      retiredDevices,
      totalUsers,
      totalOrganizations
    ] = await Promise.all([
      prisma.device.count({ where: whereClause }),
      prisma.device.count({ where: { ...whereClause, status: 'aktivni' } }),
      prisma.device.count({ where: { ...whereClause, status: 'zapuj<PERSON>no' } }),
      prisma.device.count({ where: { ...whereClause, status: 'servis' } }),
      prisma.device.count({ where: { ...whereClause, status: 'vyrazeno' } }),
      user.isSuperAdmin 
        ? prisma.user.count()
        : prisma.organizationMember.count({ where: { organizationId } }),
      user.isSuperAdmin 
        ? prisma.organization.count()
        : 1
    ])

    // Get devices by type
    const devicesByType = await prisma.device.groupBy({
      by: ['deviceType'],
      where: whereClause,
      _count: {
        id: true
      }
    })

    // Get devices by status for chart
    const devicesByStatus = [
      { name: 'Aktivní', value: activeDevices, color: '#10b981' },
      { name: 'Zapůjčeno', value: borrowedDevices, color: '#f59e0b' },
      { name: 'Servis', value: serviceDevices, color: '#ef4444' },
      { name: 'Vyřazeno', value: retiredDevices, color: '#6b7280' }
    ]

    // Get recent activity (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentDevices = await prisma.device.findMany({
      where: {
        ...whereClause,
        createdAt: {
          gte: thirtyDaysAgo
        }
      },
      select: {
        createdAt: true
      },
      orderBy: {
        createdAt: 'asc'
      }
    })

    // Group by day for chart
    const devicesByDay = recentDevices.reduce((acc, device) => {
      const day = device.createdAt.toISOString().split('T')[0]
      acc[day] = (acc[day] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const chartData = Object.entries(devicesByDay).map(([date, count]) => ({
      date,
      devices: count
    }))

    // Get top device types
    const topDeviceTypes = devicesByType
      .map(item => ({
        name: item.deviceType,
        value: item._count.id
      }))
      .sort((a, b) => b.value - a.value)
      .slice(0, 5)

    return NextResponse.json({
      overview: {
        totalDevices,
        activeDevices,
        borrowedDevices,
        serviceDevices,
        retiredDevices,
        totalUsers,
        totalOrganizations
      },
      charts: {
        devicesByStatus,
        devicesByType: topDeviceTypes,
        devicesOverTime: chartData
      }
    })

  } catch (error) {
    console.error('Dashboard stats error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    )
  }
}
