import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth()
    
    // Get organization filter
    const organizationId = user.isSuperAdmin 
      ? request.nextUrl.searchParams.get('organizationId') || undefined
      : user.organizationMembers[0]?.organizationId

    if (!organizationId && !user.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    const whereClause = user.isSuperAdmin && !organizationId 
      ? {} 
      : { organizationId }

    // Get recent devices (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const recentDevices = await prisma.device.findMany({
      where: {
        ...whereClause,
        createdAt: {
          gte: thirtyDaysAgo
        }
      },
      select: {
        id: true,
        name: true,
        deviceType: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        organization: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 20
    })

    // Get recent users (if superadmin or admin)
    let recentUsers = []
    if (user.isSuperAdmin || user.organizationMembers.some(m => m.role === 'admin')) {
      recentUsers = await prisma.user.findMany({
        where: user.isSuperAdmin ? {} : {
          organizationMembers: {
            some: {
              organizationId: organizationId
            }
          }
        },
        select: {
          id: true,
          name: true,
          email: true,
          createdAt: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 10
      })
    }

    // Get recent organizations (if superadmin)
    let recentOrganizations = []
    if (user.isSuperAdmin) {
      recentOrganizations = await prisma.organization.findMany({
        select: {
          id: true,
          name: true,
          createdAt: true
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 5
      })
    }

    // Combine and format activities
    const activities = []

    // Add device activities
    recentDevices.forEach(device => {
      activities.push({
        id: `device-${device.id}`,
        type: 'device_created' as const,
        title: 'Nové zařízení přidáno',
        description: `Zařízení "${device.name}" bylo přidáno do systému`,
        createdAt: device.createdAt.toISOString(),
        metadata: {
          deviceName: device.name,
          deviceType: device.deviceType,
          organizationName: device.organization.name,
          status: device.status
        }
      })
    })

    // Add user activities
    recentUsers.forEach(newUser => {
      activities.push({
        id: `user-${newUser.id}`,
        type: 'user_created' as const,
        title: 'Nový uživatel registrován',
        description: `Uživatel "${newUser.name}" se zaregistroval`,
        createdAt: newUser.createdAt.toISOString(),
        user: {
          name: newUser.name,
          email: newUser.email
        }
      })
    })

    // Add organization activities
    recentOrganizations.forEach(org => {
      activities.push({
        id: `org-${org.id}`,
        type: 'organization_created' as const,
        title: 'Nová organizace vytvořena',
        description: `Organizace "${org.name}" byla vytvořena`,
        createdAt: org.createdAt.toISOString(),
        metadata: {
          organizationName: org.name
        }
      })
    })

    // Sort by date and limit
    activities.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    const limitedActivities = activities.slice(0, 15)

    return NextResponse.json({ activities: limitedActivities })

  } catch (error) {
    console.error('Dashboard activities error:', error)
    return NextResponse.json(
      { error: 'Failed to fetch dashboard activities' },
      { status: 500 }
    )
  }
}
