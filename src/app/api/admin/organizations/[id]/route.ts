import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/admin/organizations/[id] - Get organization details (superadmin only)
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()

    if (!user || !user.isSuperAdmin) {
      return NextResponse.json(
        { error: 'Nedostatečná oprávnění' },
        { status: 403 }
      )
    }

    const organization = await prisma.organization.findUnique({
      where: { id: params.id },
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
                createdAt: true
              }
            }
          }
        },
        devices: {
          select: {
            id: true,
            name: true,
            evidenceNumber: true,
            status: true,
            createdAt: true
          }
        }
      }
    })

    if (!organization) {
      return NextResponse.json(
        { error: 'Organizace nenalezena' },
        { status: 404 }
      )
    }

    return NextResponse.json({ organization })
  } catch (error) {
    console.error('Get organization error:', error)
    return NextResponse.json(
      { error: 'Chyba při načítání organizace' },
      { status: 500 }
    )
  }
}

// PUT /api/admin/organizations/[id] - Update organization (superadmin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()

    if (!user || !user.isSuperAdmin) {
      return NextResponse.json(
        { error: 'Nedostatečná oprávnění' },
        { status: 403 }
      )
    }

    const { name, slug, description } = await request.json()

    if (!name || !slug) {
      return NextResponse.json(
        { error: 'Název a slug jsou povinné' },
        { status: 400 }
      )
    }

    // Check if slug is unique (excluding current organization)
    const existingOrg = await prisma.organization.findFirst({
      where: {
        slug,
        NOT: { id: params.id }
      }
    })

    if (existingOrg) {
      return NextResponse.json(
        { error: 'Organizace s tímto slug již existuje' },
        { status: 400 }
      )
    }

    const organization = await prisma.organization.update({
      where: { id: params.id },
      data: {
        name,
        slug,
        description
      }
    })

    return NextResponse.json({ organization })
  } catch (error) {
    console.error('Update organization error:', error)
    return NextResponse.json(
      { error: 'Chyba při aktualizaci organizace' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/organizations/[id] - Delete organization (superadmin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await getCurrentUser()

    if (!user || !user.isSuperAdmin) {
      return NextResponse.json(
        { error: 'Nedostatečná oprávnění' },
        { status: 403 }
      )
    }

    // Check if organization has devices
    const deviceCount = await prisma.device.count({
      where: { organizationId: params.id }
    })

    if (deviceCount > 0) {
      return NextResponse.json(
        { error: 'Nelze smazat organizaci s aktivními zařízeními' },
        { status: 400 }
      )
    }

    // Delete organization (this will cascade delete members)
    await prisma.organization.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Delete organization error:', error)
    return NextResponse.json(
      { error: 'Chyba při mazání organizace' },
      { status: 500 }
    )
  }
}
