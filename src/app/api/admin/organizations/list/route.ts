import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/admin/organizations/list - Get simple list of all organizations for superadmin
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()

    if (!user || !user.isSuperAdmin) {
      return NextResponse.json(
        { error: 'Nedostatečná oprávnění' },
        { status: 403 }
      )
    }

    const organizations = await prisma.organization.findMany({
      select: {
        id: true,
        name: true,
        slug: true
      },
      orderBy: {
        name: 'asc'
      }
    })

    return NextResponse.json({ organizations })
  } catch (error) {
    console.error('Get organizations list error:', error)
    return NextResponse.json(
      { error: 'Chyba při načítání organizací' },
      { status: 500 }
    )
  }
}
