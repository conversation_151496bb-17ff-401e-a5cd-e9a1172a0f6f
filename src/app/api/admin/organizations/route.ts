import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/admin/organizations - List all organizations (superadmin only)
export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()

    if (!user || !user.isSuperAdmin) {
      return NextResponse.json(
        { error: 'Nedostatečná oprávnění' },
        { status: 403 }
      )
    }

    const organizations = await prisma.organization.findMany({
      include: {
        members: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true
              }
            }
          }
        },
        devices: {
          select: {
            id: true,
            status: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    const organizationsWithStats = organizations.map(org => ({
      ...org,
      memberCount: org.members.length,
      deviceCount: org.devices.length,
      activeDevices: org.devices.filter(d => d.status === 'aktivni').length,
      adminCount: org.members.filter(m => m.role === 'admin').length
    }))

    return NextResponse.json({ organizations: organizationsWithStats })
  } catch (error) {
    console.error('Get organizations error:', error)
    return NextResponse.json(
      { error: 'Chyba při načítání organizací' },
      { status: 500 }
    )
  }
}

// POST /api/admin/organizations - Create new organization (superadmin only)
export async function POST(request: NextRequest) {
  try {
    const user = await getCurrentUser()

    if (!user || !user.isSuperAdmin) {
      return NextResponse.json(
        { error: 'Nedostatečná oprávnění' },
        { status: 403 }
      )
    }

    const { name, slug, description, adminEmail } = await request.json()

    if (!name || !slug) {
      return NextResponse.json(
        { error: 'Název a slug jsou povinné' },
        { status: 400 }
      )
    }

    // Check if slug is unique
    const existingOrg = await prisma.organization.findUnique({
      where: { slug }
    })

    if (existingOrg) {
      return NextResponse.json(
        { error: 'Organizace s tímto slug již existuje' },
        { status: 400 }
      )
    }

    // Create organization
    const organization = await prisma.organization.create({
      data: {
        name,
        slug,
        description
      }
    })

    // If adminEmail is provided, add user as admin
    if (adminEmail) {
      const adminUser = await prisma.user.findUnique({
        where: { email: adminEmail }
      })

      if (adminUser) {
        await prisma.organizationMember.create({
          data: {
            userId: adminUser.id,
            organizationId: organization.id,
            role: 'admin'
          }
        })
      }
    }

    return NextResponse.json({ organization })
  } catch (error) {
    console.error('Create organization error:', error)
    return NextResponse.json(
      { error: 'Chyba při vytváření organizace' },
      { status: 500 }
    )
  }
}
