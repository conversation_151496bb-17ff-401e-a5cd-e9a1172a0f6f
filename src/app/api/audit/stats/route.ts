import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from '@/lib/auth'
import { PermissionService, PERMISSIONS } from '@/lib/permissions'
import { AuditLogger } from '@/lib/audit'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const organizationId = searchParams.get('organizationId')

    // Check permissions
    const canRead = await PermissionService.hasPermission(
      session.user.id,
      PERMISSIONS.AUDIT.READ,
      organizationId || undefined
    )

    if (!canRead) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const stats = await AuditLogger.getStats(organizationId || undefined)

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching audit stats:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
