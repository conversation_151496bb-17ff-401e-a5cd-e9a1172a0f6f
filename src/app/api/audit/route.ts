import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from '@/lib/auth'
import { PermissionService, PERMISSIONS } from '@/lib/permissions'
import { AuditLogger } from '@/lib/audit'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const organizationId = searchParams.get('organizationId')
    const userId = searchParams.get('userId')
    const resource = searchParams.get('resource')
    const action = searchParams.get('action')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Check permissions
    const canRead = await PermissionService.hasPermission(
      session.user.id,
      PERMISSIONS.AUDIT.READ,
      organizationId || undefined
    )

    if (!canRead) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Build filter options
    const options: any = {
      limit,
      offset,
    }

    if (organizationId) options.organizationId = organizationId
    if (userId) options.userId = userId
    if (resource) options.resource = resource
    if (action) options.action = action
    if (startDate) options.startDate = new Date(startDate)
    if (endDate) options.endDate = new Date(endDate)

    const result = await AuditLogger.getLogs(options)

    // Log audit event for viewing audit logs
    await AuditLogger.log({
      userId: session.user.id,
      action: 'VIEW',
      resource: 'audit',
      details: {
        filters: {
          organizationId,
          userId,
          resource,
          action,
          startDate,
          endDate,
        },
        resultCount: result.logs.length,
      },
      organizationId: organizationId || undefined,
    })

    return NextResponse.json(result)
  } catch (error) {
    console.error('Error fetching audit logs:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const organizationId = searchParams.get('organizationId')
    const olderThan = searchParams.get('olderThan') // ISO date string

    // Check permissions - only superadmin or audit managers can delete logs
    const canManage = await PermissionService.hasPermission(
      session.user.id,
      PERMISSIONS.AUDIT.MANAGE,
      organizationId || undefined
    )

    if (!canManage) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    if (!olderThan) {
      return NextResponse.json({ error: 'olderThan parameter is required' }, { status: 400 })
    }

    const cutoffDate = new Date(olderThan)
    if (isNaN(cutoffDate.getTime())) {
      return NextResponse.json({ error: 'Invalid date format' }, { status: 400 })
    }

    // Build where clause
    const where: any = {
      createdAt: {
        lt: cutoffDate,
      },
    }

    if (organizationId) {
      where.organizationId = organizationId
    }

    // Count logs to be deleted
    const { prisma } = await import('@/lib/prisma')
    const countToDelete = await prisma.auditLog.count({ where })

    // Delete old audit logs
    const deleteResult = await prisma.auditLog.deleteMany({ where })

    // Log the cleanup action
    await AuditLogger.log({
      userId: session.user.id,
      action: 'CLEANUP',
      resource: 'audit',
      details: {
        deletedCount: deleteResult.count,
        cutoffDate: cutoffDate.toISOString(),
        organizationId,
      },
      organizationId: organizationId || undefined,
    })

    return NextResponse.json({
      success: true,
      deletedCount: deleteResult.count,
      cutoffDate: cutoffDate.toISOString(),
    })
  } catch (error) {
    console.error('Error cleaning up audit logs:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
