import { NextRequest, NextResponse } from 'next/server'
import { requireAuth<PERSON><PERSON> } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { handleApiError } from '@/lib/errors'
import { DEVICE_STATUSES } from '@/lib/constants'

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuthApi()

    // Get organization filter
    let organizationFilter = {}
    
    if (!user.isSuperAdmin) {
      // Regular users can only see their organization's data
      const organizationIds = user.organizationMembers.map(m => m.organization.id)
      organizationFilter = {
        organizationId: {
          in: organizationIds
        }
      }
    }

    // Get device statistics
    const [
      totalDevices,
      activeDevices,
      borrowedDevices,
      serviceDevices,
      retiredDevices,
      totalUsers,
      totalOrganizations
    ] = await Promise.all([
      // Total devices
      prisma.device.count({
        where: organizationFilter
      }),
      
      // Active devices
      prisma.device.count({
        where: {
          ...organizationFilter,
          status: DEVICE_STATUSES.AKTIVNI
        }
      }),
      
      // Borrowed devices
      prisma.device.count({
        where: {
          ...organizationFilter,
          status: DEVICE_STATUSES.ZAPUJCENO
        }
      }),
      
      // Service devices
      prisma.device.count({
        where: {
          ...organizationFilter,
          status: DEVICE_STATUSES.SERVIS
        }
      }),
      
      // Retired devices
      prisma.device.count({
        where: {
          ...organizationFilter,
          status: DEVICE_STATUSES.VYRAZENO
        }
      }),
      
      // Total users (only for superadmin or within organization)
      user.isSuperAdmin
        ? prisma.user.count()
        : prisma.organizationMember.count({
            where: {
              organizationId: {
                in: user.organizationMembers.map(m => m.organization.id)
              }
            }
          }),

      // Total organizations (only for superadmin)
      user.isSuperAdmin
        ? prisma.organization.count()
        : user.organizationMembers.length
    ])

    // Calculate percentages
    const activePercentage = totalDevices > 0 ? Math.round((activeDevices / totalDevices) * 100) : 0
    const borrowedPercentage = totalDevices > 0 ? Math.round((borrowedDevices / totalDevices) * 100) : 0
    const servicePercentage = totalDevices > 0 ? Math.round((serviceDevices / totalDevices) * 100) : 0
    const retiredPercentage = totalDevices > 0 ? Math.round((retiredDevices / totalDevices) * 100) : 0

    const stats = {
      devices: {
        total: totalDevices,
        active: activeDevices,
        borrowed: borrowedDevices,
        service: serviceDevices,
        retired: retiredDevices,
        activePercentage,
        borrowedPercentage,
        servicePercentage,
        retiredPercentage
      },
      users: {
        total: totalUsers
      },
      organizations: {
        total: totalOrganizations
      },
      summary: {
        totalDevices,
        totalUsers,
        totalOrganizations,
        healthScore: Math.round(((activeDevices + borrowedDevices) / Math.max(totalDevices, 1)) * 100)
      }
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Stats API error:', error)

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { error: errorMessage, statusCode } = handleApiError(error)
    return NextResponse.json(
      { error: errorMessage },
      { status: statusCode }
    )
  }
}
