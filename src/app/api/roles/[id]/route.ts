import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { PermissionService } from '@/lib/permissions'
import { AuditLogger } from '@/lib/audit'
import { z } from 'zod'

const updateRoleSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  permissionIds: z.array(z.string()).optional(),
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const roleId = params.id

    // Get current user from session
    const userResponse = await fetch(`${request.nextUrl.origin}/api/auth/me`, {
      headers: { cookie: request.headers.get('cookie') || '' }
    })
    
    if (!userResponse.ok) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { user: currentUser } = await userResponse.json()

    // Check permission
    const hasPermission = await PermissionService.hasPermission(
      currentUser.id,
      'roles.update'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = updateRoleSchema.parse(body)

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id: roleId },
      include: {
        rolePermissions: {
          include: {
            permission: true
          }
        }
      }
    })

    if (!existingRole) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 })
    }

    // Prevent modification of system roles
    if (existingRole.isSystem) {
      return NextResponse.json({ error: 'Cannot modify system roles' }, { status: 400 })
    }

    // Check if new name conflicts with existing role
    if (validatedData.name && validatedData.name !== existingRole.name) {
      const nameConflict = await prisma.role.findFirst({
        where: { 
          name: validatedData.name,
          id: { not: roleId }
        }
      })

      if (nameConflict) {
        return NextResponse.json({ error: 'Role with this name already exists' }, { status: 400 })
      }
    }

    // Validate permission IDs if provided
    if (validatedData.permissionIds) {
      const permissions = await prisma.permission.findMany({
        where: { id: { in: validatedData.permissionIds } }
      })

      if (permissions.length !== validatedData.permissionIds.length) {
        return NextResponse.json({ error: 'Some permission IDs are invalid' }, { status: 400 })
      }
    }

    // Update role
    const updateData: any = {}
    if (validatedData.name !== undefined) updateData.name = validatedData.name
    if (validatedData.description !== undefined) updateData.description = validatedData.description

    const role = await prisma.role.update({
      where: { id: roleId },
      data: updateData,
      include: {
        rolePermissions: {
          include: {
            permission: true
          }
        },
        _count: {
          select: {
            organizationMembers: true
          }
        }
      }
    })

    // Update permissions if provided
    if (validatedData.permissionIds) {
      // Remove existing permissions
      await prisma.rolePermission.deleteMany({
        where: { roleId: roleId }
      })

      // Add new permissions
      if (validatedData.permissionIds.length > 0) {
        await prisma.rolePermission.createMany({
          data: validatedData.permissionIds.map(permissionId => ({
            roleId: roleId,
            permissionId
          }))
        })
      }

      // Fetch updated role with new permissions
      const updatedRole = await prisma.role.findUnique({
        where: { id: roleId },
        include: {
          rolePermissions: {
            include: {
              permission: true
            }
          },
          _count: {
            select: {
              organizationMembers: true
            }
          }
        }
      })

      await AuditLogger.logFromRequest(request, {
        userId: currentUser.id,
        action: 'UPDATE',
        resource: 'role',
        resourceId: roleId,
        organizationId: null,
        details: { 
          name: validatedData.name || existingRole.name,
          permissionCount: validatedData.permissionIds.length,
          changes: validatedData
        }
      })

      return NextResponse.json(updatedRole)
    }

    await AuditLogger.logFromRequest(request, {
      userId: currentUser.id,
      action: 'UPDATE',
      resource: 'role',
      resourceId: roleId,
      organizationId: null,
      details: { 
        name: validatedData.name || existingRole.name,
        changes: validatedData
      }
    })

    return NextResponse.json(role)
  } catch (error) {
    console.error('Error updating role:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input data', details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const roleId = params.id

    // Get current user from session
    const userResponse = await fetch(`${request.nextUrl.origin}/api/auth/me`, {
      headers: { cookie: request.headers.get('cookie') || '' }
    })
    
    if (!userResponse.ok) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { user: currentUser } = await userResponse.json()

    // Check permission
    const hasPermission = await PermissionService.hasPermission(
      currentUser.id,
      'roles.delete'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Check if role exists
    const existingRole = await prisma.role.findUnique({
      where: { id: roleId },
      include: {
        _count: {
          select: {
            organizationMembers: true
          }
        }
      }
    })

    if (!existingRole) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 })
    }

    // Prevent deletion of system roles
    if (existingRole.isSystem) {
      return NextResponse.json({ error: 'Cannot delete system roles' }, { status: 400 })
    }

    // Prevent deletion if role is in use
    if (existingRole._count.organizationMembers > 0) {
      return NextResponse.json({ 
        error: 'Cannot delete role that is assigned to users' 
      }, { status: 400 })
    }

    // Delete role (this will cascade delete role permissions)
    await prisma.role.delete({
      where: { id: roleId }
    })

    await AuditLogger.logFromRequest(request, {
      userId: currentUser.id,
      action: 'DELETE',
      resource: 'role',
      resourceId: roleId,
      organizationId: null,
      details: { 
        name: existingRole.name
      }
    })

    return NextResponse.json({ message: 'Role deleted successfully' })
  } catch (error) {
    console.error('Error deleting role:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
