import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { readFile, unlink } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth()
    const fileId = params.id

    const file = await prisma.deviceFile.findUnique({
      where: { id: fileId },
      include: {
        device: {
          select: { organizationId: true },
        },
      },
    })

    if (!file) {
      return NextResponse.json(
        { error: 'Soubor nenalezen' },
        { status: 404 }
      )
    }

    // Check if user has access to organization
    const membership = user.organizationMembers.find(
      member => member.organization.id === file.device.organizationId
    )

    if (!membership) {
      return NextResponse.json(
        { error: 'Nemáte přístup k tomuto souboru' },
        { status: 403 }
      )
    }

    const filePath = join(process.cwd(), file.path)
    
    if (!existsSync(filePath)) {
      return NextResponse.json(
        { error: 'Soubor nenalezen na disku' },
        { status: 404 }
      )
    }

    const fileBuffer = await readFile(filePath)

    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': file.mimeType,
        'Content-Disposition': `attachment; filename="${file.originalName}"`,
        'Content-Length': file.size.toString(),
      },
    })
  } catch (error) {
    console.error('Download file error:', error)
    return NextResponse.json(
      { error: 'Chyba při stahování souboru' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth()
    const fileId = params.id

    const file = await prisma.deviceFile.findUnique({
      where: { id: fileId },
      include: {
        device: {
          select: { organizationId: true },
        },
      },
    })

    if (!file) {
      return NextResponse.json(
        { error: 'Soubor nenalezen' },
        { status: 404 }
      )
    }

    // Check if user has access to organization
    const membership = user.organizationMembers.find(
      member => member.organization.id === file.device.organizationId
    )

    if (!membership || membership.role === 'ctenar') {
      return NextResponse.json(
        { error: 'Nemáte oprávnění mazat soubory' },
        { status: 403 }
      )
    }

    // Delete file from disk
    const filePath = join(process.cwd(), file.path)
    if (existsSync(filePath)) {
      await unlink(filePath)
    }

    // Delete file record from database
    await prisma.deviceFile.delete({
      where: { id: fileId },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Delete file error:', error)
    return NextResponse.json(
      { error: 'Chyba při mazání souboru' },
      { status: 500 }
    )
  }
}
