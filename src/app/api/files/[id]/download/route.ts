import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'
import { readFile } from 'fs/promises'
import { existsSync } from 'fs'

const prisma = new PrismaClient()

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const fileId = params.id

    // Get file record from database
    const fileRecord = await prisma.deviceFile.findUnique({
      where: { id: fileId }
    })

    if (!fileRecord) {
      return NextResponse.json(
        { error: 'Soubor nebyl nalezen' },
        { status: 404 }
      )
    }

    // Check if file exists on disk
    if (!existsSync(fileRecord.path)) {
      return NextResponse.json(
        { error: 'Soubor nebyl nalezen na disku' },
        { status: 404 }
      )
    }

    // Read file from disk
    const fileBuffer = await readFile(fileRecord.path)

    // Return file with appropriate headers
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': fileRecord.mimeType,
        'Content-Disposition': `attachment; filename="${fileRecord.originalName}"`,
        'Content-Length': fileRecord.size.toString()
      }
    })

  } catch (error) {
    console.error('Chyba při stahování souboru:', error)
    return NextResponse.json(
      { error: 'Chyba při stahování souboru' },
      { status: 500 }
    )
  }
}
