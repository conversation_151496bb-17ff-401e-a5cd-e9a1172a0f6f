import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'
import { createId } from '@paralleldrive/cuid2'

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth()
    const formData = await request.formData()
    const file = formData.get('file') as File
    const deviceId = formData.get('deviceId') as string

    if (!file) {
      return NextResponse.json(
        { error: 'Soubor nebyl nalezen' },
        { status: 400 }
      )
    }

    if (!deviceId) {
      return NextResponse.json(
        { error: 'ID zařízení je povinné' },
        { status: 400 }
      )
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'So<PERSON><PERSON> j<PERSON> <PERSON><PERSON><PERSON><PERSON> (max. 10MB)' },
        { status: 400 }
      )
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif'
    ]

    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Nepodporovaný typ souboru' },
        { status: 400 }
      )
    }

    // Check if device exists and user has access
    const device = await prisma.device.findUnique({
      where: { id: deviceId },
      select: { id: true, organizationId: true }
    })

    if (!device) {
      return NextResponse.json(
        { error: 'Zařízení nebylo nalezeno' },
        { status: 404 }
      )
    }

    // Check if user has access to organization
    const membership = user.organizationMembers.find(
      member => member.organization.id === device.organizationId
    )

    if (!membership || membership.role === 'ctenar') {
      return NextResponse.json(
        { error: 'Nemáte oprávnění nahrávat soubory' },
        { status: 403 }
      )
    }

    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), 'uploads')
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true })
    }

    // Generate unique filename
    const fileId = createId()
    const fileExtension = file.name.split('.').pop()
    const filename = `${fileId}.${fileExtension}`
    const filepath = join(uploadsDir, filename)

    // Save file to disk
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filepath, buffer)

    // Save file record to database
    const fileRecord = await prisma.deviceFile.create({
      data: {
        id: fileId,
        deviceId: deviceId,
        filename: filename,
        originalName: file.name,
        mimeType: file.type,
        size: file.size,
        path: filepath
      }
    })

    return NextResponse.json({
      id: fileRecord.id,
      filename: fileRecord.filename,
      originalName: fileRecord.originalName,
      size: fileRecord.size,
      mimeType: fileRecord.mimeType,
      uploadedAt: fileRecord.createdAt
    })

  } catch (error) {
    console.error('Chyba při nahrávání souboru:', error)
    return NextResponse.json(
      { error: 'Chyba při nahrávání souboru' },
      { status: 500 }
    )
  }
}
