import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { PermissionService } from '@/lib/permissions'
import { AuditLogger } from '@/lib/audit'

export async function GET(request: NextRequest) {
  try {
    // Get current user from session
    const userResponse = await fetch(`${request.nextUrl.origin}/api/auth/me`, {
      headers: { cookie: request.headers.get('cookie') || '' }
    })
    
    if (!userResponse.ok) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { user: currentUser } = await userResponse.json()

    // Check permission (global permission for viewing permissions)
    const hasPermission = await PermissionService.hasPermission(
      currentUser.id,
      'permissions.view'
    )

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Fetch all permissions
    const permissions = await prisma.permission.findMany({
      orderBy: [
        { resource: 'asc' },
        { action: 'asc' }
      ]
    })

    await AuditLogger.logFromRequest(request, {
      userId: currentUser.id,
      action: 'VIEW',
      resource: 'permission',
      resourceId: null,
      organizationId: null,
      details: { count: permissions.length }
    })

    return NextResponse.json(permissions)
  } catch (error) {
    console.error('Error fetching permissions:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
