import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from '@/lib/auth'
import { PermissionService, PERMISSIONS } from '@/lib/permissions'
import { AuditLogger } from '@/lib/audit'
import { z } from 'zod'

const updateDepartmentSchema = z.object({
  name: z.string().min(1, 'Název je povinný').optional(),
  description: z.string().optional(),
  managerId: z.string().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const department = await prisma.department.findUnique({
      where: { id: params.id },
      include: {
        teams: {
          include: {
            members: {
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  }
                }
              }
            },
            _count: {
              select: {
                members: true,
              }
            }
          }
        },
        organization: {
          select: {
            id: true,
            name: true,
          }
        },
        _count: {
          select: {
            teams: true,
          }
        }
      },
    })

    if (!department) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 })
    }

    // Check permissions
    const canRead = await PermissionService.hasPermission(
      session.user.id,
      PERMISSIONS.DEPARTMENTS.READ,
      department.organizationId
    )

    if (!canRead) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Log audit event
    await AuditLogger.logDepartment(
      'VIEW',
      department.id,
      session.user.id,
      { name: department.name },
      department.organizationId,
      request
    )

    return NextResponse.json(department)
  } catch (error) {
    console.error('Error fetching department:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()

    const department = await prisma.department.findUnique({
      where: { id: params.id },
    })

    if (!department) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 })
    }

    // Check permissions
    const canUpdate = await PermissionService.hasPermission(
      session.user.id,
      PERMISSIONS.DEPARTMENTS.UPDATE,
      department.organizationId
    )

    if (!canUpdate) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Validate input
    const validatedData = updateDepartmentSchema.parse(body)

    // Check if new name already exists in organization
    if (validatedData.name && validatedData.name !== department.name) {
      const existingDepartment = await prisma.department.findFirst({
        where: {
          name: validatedData.name,
          organizationId: department.organizationId,
          id: { not: params.id },
        },
      })

      if (existingDepartment) {
        return NextResponse.json(
          { error: 'Oddělení s tímto názvem již existuje' },
          { status: 400 }
        )
      }
    }

    // Verify manager exists and belongs to organization if provided
    if (validatedData.managerId) {
      const manager = await prisma.organizationMember.findFirst({
        where: {
          userId: validatedData.managerId,
          organizationId: department.organizationId,
        },
      })

      if (!manager) {
        return NextResponse.json(
          { error: 'Vedoucí nepatří do této organizace' },
          { status: 400 }
        )
      }
    }

    const updatedDepartment = await prisma.department.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        teams: {
          include: {
            _count: {
              select: {
                members: true,
              }
            }
          }
        },
        _count: {
          select: {
            teams: true,
          }
        }
      },
    })

    // Log audit event
    await AuditLogger.logDepartment(
      'UPDATE',
      department.id,
      session.user.id,
      { 
        oldName: department.name,
        newName: updatedDepartment.name,
        changes: validatedData 
      },
      department.organizationId,
      request
    )

    return NextResponse.json(updatedDepartment)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error updating department:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const department = await prisma.department.findUnique({
      where: { id: params.id },
      include: {
        teams: true,
      },
    })

    if (!department) {
      return NextResponse.json({ error: 'Department not found' }, { status: 404 })
    }

    // Check permissions
    const canDelete = await PermissionService.hasPermission(
      session.user.id,
      PERMISSIONS.DEPARTMENTS.DELETE,
      department.organizationId
    )

    if (!canDelete) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Check if department has teams
    if (department.teams.length > 0) {
      return NextResponse.json(
        { error: 'Nelze smazat oddělení, které obsahuje týmy' },
        { status: 400 }
      )
    }

    await prisma.department.delete({
      where: { id: params.id },
    })

    // Log audit event
    await AuditLogger.logDepartment(
      'DELETE',
      department.id,
      session.user.id,
      { name: department.name },
      department.organizationId,
      request
    )

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting department:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
