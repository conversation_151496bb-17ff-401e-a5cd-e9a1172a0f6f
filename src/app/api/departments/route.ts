import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getServerSession } from '@/lib/auth'
import { PermissionService, PERMISSIONS } from '@/lib/permissions'
import { AuditLogger } from '@/lib/audit'
import { z } from 'zod'

const createDepartmentSchema = z.object({
  name: z.string().min(1, 'Název je povinný'),
  description: z.string().optional(),
  managerId: z.string().optional(),
})

const updateDepartmentSchema = createDepartmentSchema.partial()

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const organizationId = searchParams.get('organizationId')

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 })
    }

    // Check permissions
    const canRead = await PermissionService.hasPermission(
      session.user.id,
      PERMISSIONS.DEPARTMENTS.READ,
      organizationId
    )

    if (!canRead) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const departments = await prisma.department.findMany({
      where: {
        organizationId,
      },
      include: {
        teams: {
          include: {
            _count: {
              select: {
                members: true,
              }
            }
          }
        },
        _count: {
          select: {
            teams: true,
          }
        }
      },
      orderBy: {
        name: 'asc',
      },
    })

    // Log audit event
    await AuditLogger.logDepartment(
      'VIEW',
      'list',
      session.user.id,
      { count: departments.length },
      organizationId,
      request
    )

    return NextResponse.json(departments)
  } catch (error) {
    console.error('Error fetching departments:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { searchParams } = new URL(request.url)
    const organizationId = searchParams.get('organizationId')

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 })
    }

    // Check permissions
    const canCreate = await PermissionService.hasPermission(
      session.user.id,
      PERMISSIONS.DEPARTMENTS.CREATE,
      organizationId
    )

    if (!canCreate) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Validate input
    const validatedData = createDepartmentSchema.parse(body)

    // Check if department name already exists in organization
    const existingDepartment = await prisma.department.findFirst({
      where: {
        name: validatedData.name,
        organizationId,
      },
    })

    if (existingDepartment) {
      return NextResponse.json(
        { error: 'Oddělení s tímto názvem již existuje' },
        { status: 400 }
      )
    }

    // Verify manager exists and belongs to organization if provided
    if (validatedData.managerId) {
      const manager = await prisma.organizationMember.findFirst({
        where: {
          userId: validatedData.managerId,
          organizationId,
        },
      })

      if (!manager) {
        return NextResponse.json(
          { error: 'Vedoucí nepatří do této organizace' },
          { status: 400 }
        )
      }
    }

    const department = await prisma.department.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        organizationId,
        managerId: validatedData.managerId,
      },
      include: {
        teams: true,
        _count: {
          select: {
            teams: true,
          }
        }
      },
    })

    // Log audit event
    await AuditLogger.logDepartment(
      'CREATE',
      department.id,
      session.user.id,
      { name: department.name },
      organizationId,
      request
    )

    return NextResponse.json(department, { status: 201 })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      )
    }

    console.error('Error creating department:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
