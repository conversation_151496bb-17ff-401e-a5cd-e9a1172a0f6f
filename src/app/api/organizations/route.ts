import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth()

    let organizations

    if (user.isSuperAdmin) {
      // SuperAdmin vidí všechny organizace s admin právy
      const allOrgs = await prisma.organization.findMany({
        select: {
          id: true,
          name: true,
          slug: true,
        },
        orderBy: {
          name: 'asc'
        }
      })

      organizations = allOrgs.map(org => ({
        id: org.id,
        name: org.name,
        slug: org.slug,
        role: 'admin', // SuperAdmin má admin práva ve všech organizacích
      }))
    } else {
      // Ostatní uživatelé vidí POUZE své organizace - nemohou přepínat
      organizations = user.organizationMembers.map(member => ({
        id: member.organization.id,
        name: member.organization.name,
        slug: member.organization.slug,
        role: member.role,
      }))
    }

    return NextResponse.json({ organizations })
  } catch (error) {
    console.error('Get organizations error:', error)
    return NextResponse.json(
      { error: 'Chyba při načítání organizací' },
      { status: 500 }
    )
  }
}
