import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth()
    const organizationId = params.id

    // Check if user has access to organization
    const membership = user.organizationMembers.find(
      member => member.organization.id === organizationId
    )

    if (!membership) {
      return NextResponse.json(
        { error: 'Nemáte přístup k této organizaci' },
        { status: 403 }
      )
    }

    // Get device counts by status
    const devicesByStatus = await prisma.device.groupBy({
      by: ['status'],
      where: { organizationId },
      _count: { status: true },
    })

    // Get device counts by location
    const devicesByLocation = await prisma.device.groupBy({
      by: ['location'],
      where: { 
        organizationId,
        location: { not: null },
      },
      _count: { location: true },
    })

    // Get device counts by type
    const devicesByType = await prisma.device.groupBy({
      by: ['deviceType'],
      where: { 
        organizationId,
        deviceType: { not: null },
      },
      _count: { deviceType: true },
    })

    // Get total counts
    const totalDevices = await prisma.device.count({
      where: { organizationId },
    })

    const totalFiles = await prisma.deviceFile.count({
      where: {
        device: { organizationId },
      },
    })

    // Get recent activity
    const recentActivity = await prisma.deviceHistory.findMany({
      where: {
        device: { organizationId },
      },
      include: {
        device: {
          select: {
            name: true,
            evidenceNumber: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
    })

    const stats = {
      totalDevices,
      totalFiles,
      devicesByStatus: devicesByStatus.map(item => ({
        status: item.status,
        count: item._count.status,
      })),
      devicesByLocation: devicesByLocation.map(item => ({
        location: item.location,
        count: item._count.location,
      })),
      devicesByType: devicesByType.map(item => ({
        type: item.deviceType,
        count: item._count.deviceType,
      })),
      recentActivity: recentActivity.map(activity => ({
        id: activity.id,
        action: activity.action,
        deviceName: activity.device.name,
        evidenceNumber: activity.device.evidenceNumber,
        changedBy: activity.changedBy,
        createdAt: activity.createdAt,
      })),
    }

    return NextResponse.json({ stats })
  } catch (error) {
    console.error('Get stats error:', error)
    return NextResponse.json(
      { error: 'Chyba při načítání statistik' },
      { status: 500 }
    )
  }
}
