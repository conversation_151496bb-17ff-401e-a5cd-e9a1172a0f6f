import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, hasAdminAccess } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await requireAuth()
    const { id: organizationId } = await params

    // Check if user has access to organization and admin permissions
    const membership = user.organizationMembers.find(
      member => member.organization.id === organizationId
    )

    // SuperAdmin má přístup ke všem organizacím
    if (!user.isSuperAdmin) {
      if (!membership) {
        return NextResponse.json(
          { error: 'Nemáte přístup k této organizaci' },
          { status: 403 }
        )
      }

      if (membership.role !== 'admin') {
        return NextResponse.json(
          { error: 'Pouze administrátoři mohou zobrazit seznam uživatelů' },
          { status: 403 }
        )
      }
    }

    // Get organization users
    const organizationMembers = await prisma.organizationMember.findMany({
      where: {
        organizationId: organizationId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            createdAt: true,
            lastLogin: true
          }
        }
      },
      orderBy: {
        user: {
          name: 'asc'
        }
      }
    })

    const users = organizationMembers.map(member => ({
      id: member.user.id,
      name: member.user.name,
      email: member.user.email,
      role: member.role,
      createdAt: member.user.createdAt,
      lastLogin: member.user.lastLogin
    }))

    return NextResponse.json({ users })
  } catch (error) {
    console.error('Get organization users error:', error)
    return NextResponse.json(
      { error: 'Chyba při načítání uživatelů' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await requireAuth()
    const { id: organizationId } = await params
    const { email, role } = await request.json()

    // Check if user has access to organization and admin permissions
    const membership = user.organizationMembers.find(
      member => member.organization.id === organizationId
    )

    // SuperAdmin má admin práva ve všech organizacích
    if (!user.isSuperAdmin) {
      if (!membership) {
        return NextResponse.json(
          { error: 'Nemáte přístup k této organizaci' },
          { status: 403 }
        )
      }

      if (membership.role !== 'admin') {
        return NextResponse.json(
          { error: 'Pouze administrátoři mohou přidávat uživatele' },
          { status: 403 }
        )
      }
    }

    // Validate input
    if (!email || !role) {
      return NextResponse.json(
        { error: 'Email a role jsou povinné' },
        { status: 400 }
      )
    }

    if (!['admin', 'technik', 'ctenar'].includes(role)) {
      return NextResponse.json(
        { error: 'Neplatná role' },
        { status: 400 }
      )
    }

    // Check if user exists
    const targetUser = await prisma.user.findUnique({
      where: { email }
    })

    if (!targetUser) {
      return NextResponse.json(
        { error: 'Uživatel s tímto emailem neexistuje' },
        { status: 404 }
      )
    }

    // Check if user is already member
    const existingMembership = await prisma.organizationMember.findUnique({
      where: {
        userId_organizationId: {
          userId: targetUser.id,
          organizationId: organizationId
        }
      }
    })

    if (existingMembership) {
      return NextResponse.json(
        { error: 'Uživatel je již členem této organizace' },
        { status: 400 }
      )
    }

    // Add user to organization
    const newMembership = await prisma.organizationMember.create({
      data: {
        userId: targetUser.id,
        organizationId: organizationId,
        role: role
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            createdAt: true,
            lastLogin: true
          }
        }
      }
    })

    const newUser = {
      id: newMembership.user.id,
      name: newMembership.user.name,
      email: newMembership.user.email,
      role: newMembership.role,
      createdAt: newMembership.user.createdAt,
      lastLogin: newMembership.user.lastLogin
    }

    return NextResponse.json({ user: newUser })
  } catch (error) {
    console.error('Add organization user error:', error)
    return NextResponse.json(
      { error: 'Chyba při přidávání uživatele' },
      { status: 500 }
    )
  }
}
