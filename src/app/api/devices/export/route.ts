import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth()
    
    // Get organization filter
    const organizationId = user.isSuperAdmin 
      ? request.nextUrl.searchParams.get('organizationId') || undefined
      : user.organizationMembers[0]?.organizationId

    if (!organizationId && !user.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 })
    }

    const whereClause = user.isSuperAdmin && !organizationId 
      ? {} 
      : { organizationId }

    // Fetch all devices
    const devices = await prisma.device.findMany({
      where: whereClause,
      include: {
        organization: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Create CSV content
    const headers = [
      'ID',
      'Název',
      '<PERSON><PERSON>n<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      '<PERSON>',
      '<PERSON><PERSON><PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON>z<PERSON>m<PERSON>',
      'Organizace',
      '<PERSON>ytvoř<PERSON>',
      'A<PERSON>ualiz<PERSON>no'
    ]

    const csvRows = [
      headers.join(','),
      ...devices.map(device => [
        device.id,
        `"${device.name}"`,
        `"${device.evidenceNumber}"`,
        `"${device.deviceType}"`,
        `"${device.manufacturer || ''}"`,
        `"${device.model || ''}"`,
        `"${device.serialNumber || ''}"`,
        `"${device.status}"`,
        `"${device.location || ''}"`,
        `"${device.notes || ''}"`,
        `"${device.organization.name}"`,
        device.createdAt.toISOString(),
        device.updatedAt.toISOString()
      ].join(','))
    ]

    const csvContent = csvRows.join('\n')

    // Return CSV file
    return new NextResponse(csvContent, {
      headers: {
        'Content-Type': 'text/csv; charset=utf-8',
        'Content-Disposition': `attachment; filename="devices-export-${new Date().toISOString().split('T')[0]}.csv"`,
      },
    })

  } catch (error) {
    console.error('Export error:', error)
    return NextResponse.json(
      { error: 'Failed to export devices' },
      { status: 500 }
    )
  }
}
