import { NextRequest, NextResponse } from 'next/server'
import { requireAuth, hasAdminAccess } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth()
    const deviceId = params.id

    const device = await prisma.device.findUnique({
      where: { id: deviceId },
      include: {
        organization: true,
        files: true,
        history: {
          orderBy: { createdAt: 'desc' },
          take: 50,
        },
      },
    })

    if (!device) {
      return NextResponse.json(
        { error: 'Zař<PERSON>zen<PERSON> nenalezeno' },
        { status: 404 }
      )
    }

    // Check if user has access to organization
    const membership = user.organizationMembers.find(
      member => member.organization.id === device.organizationId
    )

    // SuperAdmin má přístup ke všem organizacím
    if (!membership && !user.isSuperAdmin) {
      return NextResponse.json(
        { error: 'Nemáte přístup k tomuto zařízení' },
        { status: 403 }
      )
    }

    return NextResponse.json({ device })
  } catch (error) {
    console.error('Get device error:', error)
    return NextResponse.json(
      { error: 'Chyba při načítání zařízení' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth()
    const deviceId = params.id
    const data = await request.json()

    const existingDevice = await prisma.device.findUnique({
      where: { id: deviceId },
    })

    if (!existingDevice) {
      return NextResponse.json(
        { error: 'Zařízení nenalezeno' },
        { status: 404 }
      )
    }

    // Check if user has access to organization and permissions
    const membership = user.organizationMembers.find(
      member => member.organization.id === existingDevice.organizationId
    )

    // SuperAdmin má všechna práva, jinak kontroluj membership a role
    if (!user.isSuperAdmin) {
      if (!membership || membership.role === 'ctenar') {
        return NextResponse.json(
          { error: 'Nemáte oprávnění upravovat zařízení' },
          { status: 403 }
        )
      }
    }

    const {
      evidenceNumber,
      name,
      description,
      deviceType,
      manufacturer,
      model,
      serialNumber,
      location,
      status,
      purchaseDate,
      warrantyExpiry,
      notes,
    } = data

    // Check if evidence number is unique (if changed)
    if (evidenceNumber !== existingDevice.evidenceNumber) {
      const duplicateDevice = await prisma.device.findUnique({
        where: { evidenceNumber },
      })

      if (duplicateDevice) {
        return NextResponse.json(
          { error: 'Evidenční číslo již existuje' },
          { status: 400 }
        )
      }
    }

    const updatedDevice = await prisma.device.update({
      where: { id: deviceId },
      data: {
        evidenceNumber,
        name,
        description,
        deviceType,
        manufacturer,
        model,
        serialNumber,
        location,
        status,
        purchaseDate: purchaseDate ? new Date(purchaseDate) : null,
        warrantyExpiry: warrantyExpiry ? new Date(warrantyExpiry) : null,
        notes,
      },
      include: {
        _count: {
          select: {
            files: true,
            history: true,
          },
        },
      },
    })

    // Create history entry
    await prisma.deviceHistory.create({
      data: {
        deviceId: deviceId,
        action: 'UPDATE',
        oldValues: JSON.stringify(existingDevice),
        newValues: JSON.stringify(updatedDevice),
        changedBy: user.email,
      },
    })

    return NextResponse.json({ device: updatedDevice })
  } catch (error) {
    console.error('Update device error:', error)
    return NextResponse.json(
      { error: 'Chyba při aktualizaci zařízení' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth()
    const deviceId = params.id

    const device = await prisma.device.findUnique({
      where: { id: deviceId },
    })

    if (!device) {
      return NextResponse.json(
        { error: 'Zařízení nenalezeno' },
        { status: 404 }
      )
    }

    // Check if user has access to organization and admin permissions
    const membership = user.organizationMembers.find(
      member => member.organization.id === device.organizationId
    )

    // SuperAdmin má všechna práva, jinak kontroluj admin role
    if (!user.isSuperAdmin) {
      if (!membership || membership.role !== 'admin') {
        return NextResponse.json(
          { error: 'Nemáte oprávnění mazat zařízení' },
          { status: 403 }
        )
      }
    }

    // Create history entry before deletion
    await prisma.deviceHistory.create({
      data: {
        deviceId: deviceId,
        action: 'DELETE',
        oldValues: JSON.stringify(device),
        changedBy: user.email,
      },
    })

    await prisma.device.delete({
      where: { id: deviceId },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Delete device error:', error)
    return NextResponse.json(
      { error: 'Chyba při mazání zařízení' },
      { status: 500 }
    )
  }
}
