import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { writeFile, mkdir } from 'fs/promises'
import { join } from 'path'
import { existsSync } from 'fs'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth()
    const deviceId = params.id

    const device = await prisma.device.findUnique({
      where: { id: deviceId },
      select: { organizationId: true },
    })

    if (!device) {
      return NextResponse.json(
        { error: '<PERSON>a<PERSON><PERSON><PERSON> nenalezen<PERSON>' },
        { status: 404 }
      )
    }

    // Check if user has access to organization
    const membership = user.organizationMembers.find(
      member => member.organization.id === device.organizationId
    )

    if (!membership) {
      return NextResponse.json(
        { error: 'Nemáte přístup k tomuto z<PERSON>řízení' },
        { status: 403 }
      )
    }

    const files = await prisma.deviceFile.findMany({
      where: { deviceId },
      orderBy: { createdAt: 'desc' },
    })

    return NextResponse.json({ files })
  } catch (error) {
    console.error('Get files error:', error)
    return NextResponse.json(
      { error: 'Chyba při načítání souborů' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth()
    const deviceId = params.id

    const device = await prisma.device.findUnique({
      where: { id: deviceId },
      select: { organizationId: true },
    })

    if (!device) {
      return NextResponse.json(
        { error: 'Zařízení nenalezeno' },
        { status: 404 }
      )
    }

    // Check if user has access to organization
    const membership = user.organizationMembers.find(
      member => member.organization.id === device.organizationId
    )

    if (!membership || membership.role === 'ctenar') {
      return NextResponse.json(
        { error: 'Nemáte oprávnění nahrávat soubory' },
        { status: 403 }
      )
    }

    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { error: 'Soubor je povinný' },
        { status: 400 }
      )
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'Soubor je příliš velký (max 10MB)' },
        { status: 400 }
      )
    }

    // Generate unique filename
    const timestamp = Date.now()
    const extension = file.name.split('.').pop()
    const filename = `${timestamp}-${Math.random().toString(36).substring(2)}.${extension}`
    
    // Create upload directory if it doesn't exist
    const uploadDir = join(process.cwd(), 'uploads', deviceId)
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // Save file to disk
    const filePath = join(uploadDir, filename)
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)

    // Save file info to database
    const deviceFile = await prisma.deviceFile.create({
      data: {
        deviceId,
        filename,
        originalName: file.name,
        mimeType: file.type,
        size: file.size,
        path: `uploads/${deviceId}/${filename}`,
      },
    })

    return NextResponse.json({ file: deviceFile })
  } catch (error) {
    console.error('Upload file error:', error)
    return NextResponse.json(
      { error: 'Chyba při nahrávání souboru' },
      { status: 500 }
    )
  }
}
