import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import QRCode from 'qrcode'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await requireAuth()
    const deviceId = params.id

    // Get device from database
    const device = await prisma.device.findUnique({
      where: { id: deviceId },
      select: {
        id: true,
        evidenceNumber: true,
        name: true,
        type: true,
        brand: true,
        model: true,
        organizationId: true
      }
    })

    if (!device) {
      return NextResponse.json(
        { error: 'Zařízení nebylo nalezeno' },
        { status: 404 }
      )
    }

    // Check if user has access to organization
    const membership = user.organizationMembers.find(
      member => member.organization.id === device.organizationId
    )

    if (!membership) {
      return NextResponse.json(
        { error: 'Nemáte přístup k tomuto z<PERSON>řízení' },
        { status: 403 }
      )
    }

    // Create QR code data
    const qrData = {
      id: device.id,
      evidenceNumber: device.evidenceNumber,
      name: device.name,
      type: device.type,
      brand: device.brand,
      model: device.model,
      url: `${request.nextUrl.origin}/dashboard/devices/${device.id}`
    }

    // Generate QR code as SVG
    const qrCodeSvg = await QRCode.toString(JSON.stringify(qrData), {
      type: 'svg',
      width: 256,
      margin: 2,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })

    // Return SVG response
    return new NextResponse(qrCodeSvg, {
      headers: {
        'Content-Type': 'image/svg+xml',
        'Cache-Control': 'public, max-age=3600'
      }
    })

  } catch (error) {
    console.error('Chyba při generování QR kódu:', error)
    return NextResponse.json(
      { error: 'Chyba při generování QR kódu' },
      { status: 500 }
    )
  }
}
