import { NextRequest, NextResponse } from 'next/server'
import { requireA<PERSON><PERSON><PERSON>, hasAdminAccess } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { AuditLogger } from '@/lib/audit'

export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth<PERSON><PERSON>()
    const { searchParams } = new URL(request.url)
    
    const organizationId = searchParams.get('organizationId')
    const search = searchParams.get('search')
    const status = searchParams.get('status')
    const location = searchParams.get('location')
    const deviceType = searchParams.get('deviceType')
    const limit = searchParams.get('limit')

    let where: any = {}

    if (organizationId) {
      // Check if user has access to organization
      const membership = user.organizationMembers.find(
        member => member.organization.id === organizationId
      )

      // SuperAdmin má přístup ke všem organizacím
      // Ostatní uživatelé pouze ke svým organizacím
      if (!user.isSuperAdmin && !membership) {
        return NextResponse.json(
          { error: 'Nemáte přístup k této organizaci' },
          { status: 403 }
        )
      }

      where.organizationId = organizationId
    } else {
      // If no organizationId provided, filter by user's organizations
      if (!user.isSuperAdmin) {
        const userOrganizationIds = user.organizationMembers.map(
          member => member.organization.id
        )
        where.organizationId = {
          in: userOrganizationIds
        }
      }
      // SuperAdmin can see all devices if no organizationId specified
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { evidenceNumber: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { manufacturer: { contains: search, mode: 'insensitive' } },
        { model: { contains: search, mode: 'insensitive' } },
        { serialNumber: { contains: search, mode: 'insensitive' } },
      ]
    }

    if (status) {
      where.status = status
    }

    if (location) {
      where.location = { contains: location, mode: 'insensitive' }
    }

    if (deviceType) {
      where.deviceType = { contains: deviceType, mode: 'insensitive' }
    }

    const queryOptions: any = {
      where,
      include: {
        _count: {
          select: {
            files: true,
            history: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    }

    // Add limit if specified
    if (limit) {
      const limitNum = parseInt(limit, 10)
      if (!isNaN(limitNum) && limitNum > 0) {
        queryOptions.take = limitNum
      }
    }

    const devices = await prisma.device.findMany(queryOptions)

    // Log device view action
    await AuditLogger.logFromRequest(request, {
      userId: user.id,
      action: 'VIEW',
      resource: 'device',
      resourceId: null,
      organizationId: organizationId || null,
      details: {
        count: devices.length,
        filters: { search, status, location, deviceType }
      }
    })

    return NextResponse.json({ devices })
  } catch (error) {
    console.error('Get devices error:', error)

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    return NextResponse.json(
      { error: 'Chyba při načítání zařízení' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuthApi()
    const data = await request.json()

    const {
      organizationId,
      evidenceNumber,
      name,
      description,
      deviceType,
      manufacturer,
      model,
      serialNumber,
      location,
      status = 'aktivni',
      purchaseDate,
      warrantyExpiry,
      notes,
    } = data

    if (!organizationId || !evidenceNumber || !name) {
      return NextResponse.json(
        { error: 'Organizace, evidenční číslo a název jsou povinné' },
        { status: 400 }
      )
    }

    // Check if user has access to organization and permissions
    const membership = user.organizationMembers.find(
      member => member.organization.id === organizationId
    )

    // SuperAdmin má všechna práva, jinak kontroluj membership a role
    if (!user.isSuperAdmin) {
      if (!membership || (membership.role === 'ctenar')) {
        return NextResponse.json(
          { error: 'Nemáte oprávnění vytvářet zařízení' },
          { status: 403 }
        )
      }
    }

    // Check if evidence number is unique
    const existingDevice = await prisma.device.findUnique({
      where: { evidenceNumber },
    })

    if (existingDevice) {
      return NextResponse.json(
        { error: 'Evidenční číslo již existuje' },
        { status: 400 }
      )
    }

    const device = await prisma.device.create({
      data: {
        organizationId,
        evidenceNumber,
        name,
        description,
        deviceType,
        manufacturer,
        model,
        serialNumber,
        location,
        status,
        purchaseDate: purchaseDate ? new Date(purchaseDate) : null,
        warrantyExpiry: warrantyExpiry ? new Date(warrantyExpiry) : null,
        notes,
      },
      include: {
        _count: {
          select: {
            files: true,
            history: true,
          },
        },
      },
    })

    // Create history entry
    await prisma.deviceHistory.create({
      data: {
        deviceId: device.id,
        action: 'INSERT',
        newValues: JSON.stringify(device),
        changedBy: user.email,
      },
    })

    // Log device creation
    await AuditLogger.logFromRequest(request, {
      userId: user.id,
      action: 'CREATE',
      resource: 'device',
      resourceId: device.id,
      organizationId: organizationId,
      details: {
        name: device.name,
        evidenceNumber: device.evidenceNumber,
        deviceType: device.deviceType
      }
    })

    return NextResponse.json({ device })
  } catch (error) {
    console.error('Create device error:', error)
    return NextResponse.json(
      { error: 'Chyba při vytváření zařízení' },
      { status: 500 }
    )
  }
}
