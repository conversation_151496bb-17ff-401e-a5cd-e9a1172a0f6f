import { NextRequest, NextResponse } from 'next/server'
import { authenticateUser, createSession, setSessionCookie } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email a heslo jsou povinné' },
        { status: 400 }
      )
    }

    const user = await authenticateUser(email, password)
    if (!user) {
      return NextResponse.json(
        { error: 'Neplatné přihla<PERSON>ova<PERSON> údaje' },
        { status: 401 }
      )
    }

    const sessionToken = await createSession(user.id)
    
    const response = NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        isSuperAdmin: user.isSuperAdmin,
      }
    })

    response.cookies.set('session', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 30 * 24 * 60 * 60, // 30 days
      path: '/',
    })

    return response
  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { error: '<PERSON>yba při p<PERSON><PERSON>la<PERSON>n<PERSON>' },
      { status: 500 }
    )
  }
}
