import { NextRequest, NextResponse } from 'next/server'
import { createUser, createSession } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { generateSlug } from '@/lib/utils'

export async function POST(request: NextRequest) {
  try {
    const { email, password, name, organizationName } = await request.json()

    if (!email || !password || !name || !organizationName) {
      return NextResponse.json(
        { error: 'Všechna pole jsou povinná' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'Uživatel s tímto emailem již existuje' },
        { status: 400 }
      )
    }

    // Create user and organization in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create user
      const user = await createUser(email, password, name)

      // Create organization
      const orgSlug = generateSlug(organizationName)
      const organization = await tx.organization.create({
        data: {
          name: organizationName,
          slug: orgSlug,
        },
      })

      // Add user as admin to organization
      await tx.organizationMember.create({
        data: {
          userId: user.id,
          organizationId: organization.id,
          role: 'admin',
        },
      })

      return { user, organization }
    }, {
      timeout: 10000 // 10 seconds timeout
    })

    const sessionToken = await createSession(result.user.id)
    
    const response = NextResponse.json({ 
      success: true,
      user: {
        id: result.user.id,
        email: result.user.email,
        name: result.user.name,
      },
      organization: {
        id: result.organization.id,
        name: result.organization.name,
        slug: result.organization.slug,
      }
    })

    response.cookies.set('session', sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 30 * 24 * 60 * 60, // 30 days
      path: '/',
    })

    return response
  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { error: 'Chyba při registraci' },
      { status: 500 }
    )
  }
}
