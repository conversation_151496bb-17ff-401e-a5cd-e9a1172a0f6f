import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { PermissionService } from '@/lib/permissions'
import { AuditLogger } from '@/lib/audit'
import { z } from 'zod'

const updateRoleSchema = z.object({
  roleId: z.string(),
})

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const organizationId = searchParams.get('organizationId')
    const userId = params.id

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 })
    }

    // Get current user from session
    const userResponse = await fetch(`${request.nextUrl.origin}/api/auth/me`, {
      headers: { cookie: request.headers.get('cookie') || '' }
    })
    
    if (!userResponse.ok) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { user: currentUser } = await userResponse.json()

    // Check permission
    const hasPermission = await PermissionService.hasPermission(
      currentUser.id,
      'users.update',
      organizationId
    )

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = updateRoleSchema.parse(body)

    // Check if user exists and is member of organization
    const member = await prisma.organizationMember.findFirst({
      where: {
        userId: userId,
        organizationId: organizationId
      },
      include: {
        user: true,
        role: true
      }
    })

    if (!member) {
      return NextResponse.json({ error: 'User not found in organization' }, { status: 404 })
    }

    // Check if role exists
    const role = await prisma.role.findUnique({
      where: { id: validatedData.roleId }
    })

    if (!role) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 })
    }

    // Update user role
    const updatedMember = await prisma.organizationMember.update({
      where: { id: member.id },
      data: {
        roleId: validatedData.roleId,
        legacyRole: null // Clear legacy role when setting new role
      },
      include: {
        user: true,
        role: true
      }
    })

    await AuditLogger.logFromRequest(request, {
      userId: currentUser.id,
      action: 'UPDATE',
      resource: 'user',
      resourceId: userId,
      organizationId,
      details: { 
        action: 'role_updated',
        oldRoleId: member.roleId,
        oldRoleName: member.role?.name,
        newRoleId: validatedData.roleId,
        newRoleName: role.name,
        userEmail: member.user.email
      }
    })

    return NextResponse.json(updatedMember)
  } catch (error) {
    console.error('Error updating user role:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input data', details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
