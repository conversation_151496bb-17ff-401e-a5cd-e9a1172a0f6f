import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { PermissionService } from '@/lib/permissions'
import { AuditLogger } from '@/lib/audit'
import { z } from 'zod'
import bcrypt from 'bcryptjs'

const createUserSchema = z.object({
  email: z.string().email(),
  name: z.string().optional(),
  password: z.string().min(6),
  roleId: z.string(),
})

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const organizationId = searchParams.get('organizationId')

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 })
    }

    // Get current user from session
    const userResponse = await fetch(`${request.nextUrl.origin}/api/auth/me`, {
      headers: { cookie: request.headers.get('cookie') || '' }
    })
    
    if (!userResponse.ok) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { user: currentUser } = await userResponse.json()

    // Check permission
    const hasPermission = await PermissionService.hasPermission(
      currentUser.id,
      'users.view',
      organizationId
    )

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Fetch users in organization
    const users = await prisma.user.findMany({
      where: {
        organizationMembers: {
          some: {
            organizationId: organizationId
          }
        }
      },
      include: {
        organizationMembers: {
          where: {
            organizationId: organizationId
          },
          include: {
            role: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    await AuditLogger.logFromRequest(request, {
      userId: currentUser.id,
      action: 'VIEW',
      resource: 'user',
      resourceId: null,
      organizationId,
      details: { count: users.length }
    })

    return NextResponse.json(users)
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const organizationId = searchParams.get('organizationId')

    if (!organizationId) {
      return NextResponse.json({ error: 'Organization ID is required' }, { status: 400 })
    }

    // Get current user from session
    const userResponse = await fetch(`${request.nextUrl.origin}/api/auth/me`, {
      headers: { cookie: request.headers.get('cookie') || '' }
    })
    
    if (!userResponse.ok) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { user: currentUser } = await userResponse.json()

    // Check permission
    const hasPermission = await PermissionService.hasPermission(
      currentUser.id,
      'users.create',
      organizationId
    )

    if (!hasPermission) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    const body = await request.json()
    const validatedData = createUserSchema.parse(body)

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: validatedData.email }
    })

    if (existingUser) {
      // Check if user is already in this organization
      const existingMember = await prisma.organizationMember.findFirst({
        where: {
          userId: existingUser.id,
          organizationId: organizationId
        }
      })

      if (existingMember) {
        return NextResponse.json({ error: 'User is already a member of this organization' }, { status: 400 })
      }

      // Add existing user to organization
      const member = await prisma.organizationMember.create({
        data: {
          userId: existingUser.id,
          organizationId: organizationId,
          roleId: validatedData.roleId
        }
      })

      await AuditLogger.logFromRequest(request, {
        userId: currentUser.id,
        action: 'CREATE',
        resource: 'user',
        resourceId: existingUser.id,
        organizationId,
        details: { 
          action: 'added_to_organization',
          email: validatedData.email,
          roleId: validatedData.roleId
        }
      })

      return NextResponse.json({ user: existingUser, member })
    }

    // Create new user
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    const user = await prisma.user.create({
      data: {
        email: validatedData.email,
        name: validatedData.name,
        password: hashedPassword,
        organizationMembers: {
          create: {
            organizationId: organizationId,
            roleId: validatedData.roleId
          }
        }
      },
      include: {
        organizationMembers: {
          include: {
            role: true
          }
        }
      }
    })

    await AuditLogger.logFromRequest(request, {
      userId: currentUser.id,
      action: 'CREATE',
      resource: 'user',
      resourceId: user.id,
      organizationId,
      details: { 
        email: validatedData.email,
        roleId: validatedData.roleId
      }
    })

    // Remove password from response
    const { password, ...userWithoutPassword } = user

    return NextResponse.json(userWithoutPassword, { status: 201 })
  } catch (error) {
    console.error('Error creating user:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({ error: 'Invalid input data', details: error.errors }, { status: 400 })
    }

    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
