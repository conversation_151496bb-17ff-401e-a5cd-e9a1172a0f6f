import Link from 'next/link'

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-gray-100 flex items-center justify-center p-6">
      <div className="max-w-md w-full bg-white rounded-2xl shadow-xl p-8 text-center">
        <div className="mb-6">
          <div className="w-16 h-16 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl mx-auto mb-4 flex items-center justify-center shadow-orange">
            <span className="text-2xl font-bold text-white">R</span>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">RokitBox</h1>
          <p className="text-gray-600">Moderní evidenční systém hardwaru</p>
        </div>

        <div className="space-y-4">
          <Link href="/auth/login">
            <button className="w-full gradient-orange text-white font-medium py-3 px-6 rounded-xl hover:shadow-orange-lg transition-all duration-200 transform hover:scale-105">
              Přihlásit se
            </button>
          </Link>

          <Link href="/auth/register">
            <button className="w-full border border-gray-200 text-gray-700 font-medium py-3 px-6 rounded-xl hover:bg-gray-50 transition-all duration-200">
              Registrovat se
            </button>
          </Link>
        </div>

        <div className="mt-8 text-sm text-gray-500">
          <p>✨ Moderní design s oranžovo-šedým schématem</p>
        </div>
      </div>
    </div>
  )
}
