'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { SwipeActions } from '@/components/ui/swipe-actions'
import { PullToRefresh } from '@/components/ui/pull-to-refresh'
import { Plus, Search, Filter, Monitor, Smartphone, Laptop, Server, QrCode, Edit, Trash2 } from 'lucide-react'
import Link from 'next/link'

interface Device {
  id: string
  evidenceNumber: string
  name: string
  description?: string
  deviceType?: string
  manufacturer?: string
  model?: string
  serialNumber?: string
  status: 'aktivni' | 'zapujceno' | 'servis' | 'vyrazeno'
  location?: string
  assignedTo?: string
  purchaseDate?: string
  warrantyExpiry?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

const statusColors = {
  aktivni: 'bg-green-100 text-green-800 border-green-200',
  zapujceno: 'bg-blue-100 text-blue-800 border-blue-200',
  servis: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  vyrazeno: 'bg-red-100 text-red-800 border-red-200'
}

const statusLabels = {
  aktivni: 'Aktivní',
  zapujceno: 'Zapůjčeno',
  servis: 'Servis',
  vyrazeno: 'Vyřazeno'
}

const typeIcons = {
  laptop: Laptop,
  desktop: Monitor,
  server: Server,
  mobile: Smartphone,
  tablet: Smartphone,
  other: Monitor
}

export default function DevicesPage() {
  const router = useRouter()
  const [devices, setDevices] = useState<Device[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    fetchDevices()
  }, [])

  const fetchDevices = async () => {
    try {
      // First get current user and their organizations
      const userResponse = await fetch('/api/auth/me')
      if (!userResponse.ok) {
        router.push('/auth/login')
        return
      }

      const userData = await userResponse.json()
      if (!userData.user.organizations || userData.user.organizations.length === 0) {
        console.error('Uživatel nemá žádné organizace')
        setLoading(false)
        return
      }

      // Use the first organization for now
      const organizationId = userData.user.organizations[0].id

      const response = await fetch(`/api/devices?organizationId=${organizationId}`)
      if (response.ok) {
        const data = await response.json()
        setDevices(data.devices || [])
      }
    } catch (error) {
      console.error('Chyba při načítání zařízení:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredDevices = devices.filter(device => {
    const matchesSearch = searchTerm === '' ||
      (device.name && device.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (device.evidenceNumber && device.evidenceNumber.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (device.manufacturer && device.manufacturer.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (device.model && device.model.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (device.serialNumber && device.serialNumber.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (device.location && device.location.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesStatus = statusFilter === 'all' || device.status === statusFilter

    return matchesSearch && matchesStatus
  })

  const handleDeleteDevice = async (deviceId: string) => {
    if (!confirm('Opravdu chcete smazat toto zařízení?')) return

    try {
      const response = await fetch(`/api/devices/${deviceId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        setDevices(devices.filter(d => d.id !== deviceId))
      }
    } catch (error) {
      console.error('Chyba při mazání zařízení:', error)
    }
  }

  const handleRefresh = async () => {
    await fetchDevices()
  }

  if (!mounted || loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Načítám zařízení...</p>
        </div>
      </div>
    )
  }

  return (
    <PullToRefresh onRefresh={handleRefresh} className="min-h-screen">
      <div className="space-y-6 p-4 sm:p-6">
        {/* Header */}
        <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Zařízení</h1>
            <p className="text-gray-600 text-sm sm:text-base">Správa hardwarového vybavení</p>
          </div>
          <Link href="/dashboard/devices/new" className="hidden sm:block">
            <Button className="gradient-orange hover:shadow-orange text-white rounded-xl transition-all duration-200 transform hover:scale-105 touch-manipulation active:scale-95">
              <Plus className="h-4 w-4 mr-2" />
              Přidat zařízení
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <Card className="border-0 shadow-lg">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <Input
                    placeholder="Hledat podle názvu, evidence, značky..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 text-base sm:text-sm"
                  />
                </div>
              </div>
              <div className="sm:w-48">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="all">Všechny stavy</option>
                <option value="aktivni">Aktivní</option>
                <option value="zapujceno">Zapůjčeno</option>
                <option value="servis">Servis</option>
                <option value="vyrazeno">Vyřazeno</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Devices Grid */}
      {filteredDevices.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Monitor className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm || statusFilter !== 'all' ? 'Žádná zařízení nenalezena' : 'Zatím nemáte žádná zařízení'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchTerm || statusFilter !== 'all' 
                ? 'Zkuste změnit vyhledávací kritéria'
                : 'Začněte přidáním prvního zařízení do evidence'
              }
            </p>
            {!searchTerm && statusFilter === 'all' && (
              <Link href="/dashboard/devices/new">
                <Button className="gradient-orange hover:shadow-orange text-white rounded-xl transition-all duration-200 transform hover:scale-105">
                  <Plus className="h-4 w-4 mr-2" />
                  Přidat první zařízení
                </Button>
              </Link>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
          {filteredDevices.map((device) => {
            const IconComponent = typeIcons[device.deviceType as keyof typeof typeIcons] || Monitor

            const swipeActions = [
              {
                icon: <Edit className="h-5 w-5" />,
                label: 'Upravit',
                onClick: () => router.push(`/dashboard/devices/${device.id}/edit`),
                side: 'left' as const,
                className: 'bg-blue-500 hover:bg-blue-600'
              },
              {
                icon: <Trash2 className="h-5 w-5" />,
                label: 'Smazat',
                onClick: () => handleDeleteDevice(device.id),
                side: 'right' as const,
                className: 'bg-red-500 hover:bg-red-600'
              }
            ]

            return (
              <SwipeActions key={device.id} actions={swipeActions} className="md:pointer-events-none">
                <Card className="hover:shadow-lg transition-shadow touch-manipulation active:scale-95">
                <CardHeader className="pb-3 p-4 sm:p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3 min-w-0 flex-1">
                      <div className="p-2 bg-orange-100 rounded-lg flex-shrink-0">
                        <IconComponent className="h-5 w-5 text-orange-600" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <CardTitle className="text-base sm:text-lg truncate">{device.name}</CardTitle>
                        <CardDescription className="text-xs sm:text-sm truncate">
                          {device.evidenceNumber}
                        </CardDescription>
                      </div>
                    </div>
                    <Badge className={`${statusColors[device.status]} text-xs sm:text-sm flex-shrink-0 ml-2`}>
                      {statusLabels[device.status]}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-3 p-4 sm:p-6 pt-0">
                  <div className="space-y-2 text-xs sm:text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Výrobce:</span>
                      <span className="font-medium">{device.manufacturer || '-'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Model:</span>
                      <span className="font-medium">{device.model || '-'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Umístění:</span>
                      <span className="font-medium">{device.location || '-'}</span>
                    </div>
                    {device.assignedTo && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Přiřazeno:</span>
                        <span className="font-medium">{device.assignedTo}</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between pt-3 border-t">
                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(`/api/devices/${device.id}/qr`, '_blank')}
                        className="touch-manipulation active:scale-95"
                      >
                        <QrCode className="h-4 w-4" />
                      </Button>
                      <Link href={`/dashboard/devices/${device.id}/edit`} className="hidden md:block">
                        <Button variant="outline" size="sm" className="touch-manipulation active:scale-95">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteDevice(device.id)}
                      className="text-red-600 hover:text-red-700 hover:bg-red-50 touch-manipulation active:scale-95 hidden md:block"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
              </SwipeActions>
            )
          })}
        </div>
      )}
      </div>
    </PullToRefresh>
  )
}
