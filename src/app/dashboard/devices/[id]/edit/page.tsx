'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ArrowLeft, Save } from 'lucide-react'
import Link from 'next/link'

interface Device {
  id: string
  evidenceNumber: string
  name: string
  description?: string
  deviceType?: string
  manufacturer?: string
  model?: string
  serialNumber?: string
  location?: string
  status: string
  purchaseDate?: string
  warrantyExpiry?: string
  notes?: string
}

export default function EditDevicePage() {
  const router = useRouter()
  const params = useParams()
  const deviceId = params.id as string
  
  const [device, setDevice] = useState<Device | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [formData, setFormData] = useState({
    evidenceNumber: '',
    name: '',
    description: '',
    deviceType: '',
    manufacturer: '',
    model: '',
    serialNumber: '',
    location: '',
    status: 'aktivni',
    purchaseDate: '',
    warrantyExpiry: '',
    notes: ''
  })

  useEffect(() => {
    if (deviceId) {
      fetchDevice()
    }
  }, [deviceId])

  const fetchDevice = async () => {
    try {
      const response = await fetch(`/api/devices/${deviceId}`)
      if (response.ok) {
        const data = await response.json()
        setDevice(data.device)
        setFormData({
          evidenceNumber: data.device.evidenceNumber || '',
          name: data.device.name || '',
          description: data.device.description || '',
          deviceType: data.device.deviceType || '',
          manufacturer: data.device.manufacturer || '',
          model: data.device.model || '',
          serialNumber: data.device.serialNumber || '',
          location: data.device.location || '',
          status: data.device.status || 'aktivni',
          purchaseDate: data.device.purchaseDate ? data.device.purchaseDate.split('T')[0] : '',
          warrantyExpiry: data.device.warrantyExpiry ? data.device.warrantyExpiry.split('T')[0] : '',
          notes: data.device.notes || ''
        })
      } else {
        setError('Zařízení nenalezeno')
      }
    } catch (error) {
      console.error('Chyba při načítání zařízení:', error)
      setError('Chyba při načítání zařízení')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)
    setError('')

    try {
      const response = await fetch(`/api/devices/${deviceId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        router.push(`/dashboard/devices/${deviceId}`)
      } else {
        setError(data.error || 'Chyba při aktualizaci zařízení')
      }
    } catch (err) {
      setError('Došlo k neočekávané chybě')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Načítám zařízení...</p>
        </div>
      </div>
    )
  }

  if (!device) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/devices">
            <Button variant="outline" size="sm" className="rounded-xl">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Zpět na seznam
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Zařízení nenalezeno</h1>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href={`/dashboard/devices/${deviceId}`}>
          <Button variant="outline" size="sm" className="rounded-xl">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Zpět na detail
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Upravit zařízení</h1>
          <p className="text-gray-600">{device.name} ({device.evidenceNumber})</p>
        </div>
      </div>

      {/* Form */}
      <Card className="shadow-orange-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader>
          <CardTitle>Základní informace</CardTitle>
          <CardDescription>
            Upravte údaje o zařízení
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <Alert variant="destructive" className="border-red-200 bg-red-50">
                <AlertDescription className="text-red-800">{error}</AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="evidenceNumber" className="text-sm font-medium text-gray-700">
                  Evidenční číslo *
                </Label>
                <Input
                  id="evidenceNumber"
                  name="evidenceNumber"
                  type="text"
                  value={formData.evidenceNumber}
                  onChange={handleInputChange}
                  required
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                  Název zařízení *
                </Label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="deviceType" className="text-sm font-medium text-gray-700">
                  Typ zařízení
                </Label>
                <select
                  id="deviceType"
                  name="deviceType"
                  value={formData.deviceType}
                  onChange={handleInputChange}
                  className="w-full h-12 px-3 border border-gray-200 rounded-xl focus:border-orange-500 focus:ring-orange-500 transition-all duration-200"
                >
                  <option value="">Vyberte typ</option>
                  <option value="laptop">Notebook</option>
                  <option value="desktop">Stolní počítač</option>
                  <option value="server">Server</option>
                  <option value="mobile">Mobilní telefon</option>
                  <option value="tablet">Tablet</option>
                  <option value="monitor">Monitor</option>
                  <option value="printer">Tiskárna</option>
                  <option value="other">Ostatní</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status" className="text-sm font-medium text-gray-700">
                  Stav
                </Label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full h-12 px-3 border border-gray-200 rounded-xl focus:border-orange-500 focus:ring-orange-500 transition-all duration-200"
                >
                  <option value="aktivni">Aktivní</option>
                  <option value="zapujceno">Zapůjčeno</option>
                  <option value="servis">Servis</option>
                  <option value="vyrazeno">Vyřazeno</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="manufacturer" className="text-sm font-medium text-gray-700">
                  Výrobce
                </Label>
                <Input
                  id="manufacturer"
                  name="manufacturer"
                  type="text"
                  value={formData.manufacturer}
                  onChange={handleInputChange}
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="model" className="text-sm font-medium text-gray-700">
                  Model
                </Label>
                <Input
                  id="model"
                  name="model"
                  type="text"
                  value={formData.model}
                  onChange={handleInputChange}
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="serialNumber" className="text-sm font-medium text-gray-700">
                  Sériové číslo
                </Label>
                <Input
                  id="serialNumber"
                  name="serialNumber"
                  type="text"
                  value={formData.serialNumber}
                  onChange={handleInputChange}
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="location" className="text-sm font-medium text-gray-700">
                  Umístění
                </Label>
                <Input
                  id="location"
                  name="location"
                  type="text"
                  value={formData.location}
                  onChange={handleInputChange}
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="purchaseDate" className="text-sm font-medium text-gray-700">
                  Datum nákupu
                </Label>
                <Input
                  id="purchaseDate"
                  name="purchaseDate"
                  type="date"
                  value={formData.purchaseDate}
                  onChange={handleInputChange}
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="warrantyExpiry" className="text-sm font-medium text-gray-700">
                  Konec záruky
                </Label>
                <Input
                  id="warrantyExpiry"
                  name="warrantyExpiry"
                  type="date"
                  value={formData.warrantyExpiry}
                  onChange={handleInputChange}
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium text-gray-700">
                Popis
              </Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes" className="text-sm font-medium text-gray-700">
                Poznámky
              </Label>
              <Textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
              />
            </div>

            <div className="flex justify-end space-x-4 pt-6">
              <Link href={`/dashboard/devices/${deviceId}`}>
                <Button variant="outline" className="rounded-xl">
                  Zrušit
                </Button>
              </Link>
              <Button
                type="submit"
                disabled={saving}
                className="gradient-orange hover:shadow-orange text-white rounded-xl transition-all duration-200 transform hover:scale-105"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Ukládám...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Uložit změny
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
