'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Save, QrCode, Edit, History, Printer } from 'lucide-react'
import Link from 'next/link'
import FileUpload from '@/components/devices/file-upload'

interface Device {
  id: string
  evidenceNumber: string
  name: string
  type: string
  brand: string
  model: string
  serialNumber: string
  status: 'aktivni' | 'zapujceno' | 'servis' | 'vyrazeno'
  location: string
  assignedTo?: string
  purchaseDate: string
  warrantyUntil?: string
  notes?: string
  createdAt: string
  updatedAt: string
}

const statusColors = {
  aktivni: 'bg-green-100 text-green-800 border-green-200',
  zapujceno: 'bg-blue-100 text-blue-800 border-blue-200',
  servis: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  vyrazeno: 'bg-red-100 text-red-800 border-red-200'
}

const statusLabels = {
  aktivni: 'Aktivní',
  zapujceno: 'Zapůjčeno',
  servis: 'Servis',
  vyrazeno: 'Vyřazeno'
}

export default function DeviceDetailPage() {
  const [device, setDevice] = useState<Device | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    type: 'laptop',
    brand: '',
    model: '',
    serialNumber: '',
    status: 'active',
    location: '',
    assignedTo: '',
    purchaseDate: '',
    warrantyUntil: '',
    notes: ''
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [isEditing, setIsEditing] = useState(false)
  const router = useRouter()
  const params = useParams()

  useEffect(() => {
    if (params.id) {
      fetchDevice(params.id as string)
    }
  }, [params.id])

  const fetchDevice = async (deviceId: string) => {
    try {
      const response = await fetch(`/api/devices/${deviceId}`)
      if (response.ok) {
        const data = await response.json()
        setDevice(data)
        setFormData({
          name: data.name || '',
          type: data.type || 'laptop',
          brand: data.brand || '',
          model: data.model || '',
          serialNumber: data.serialNumber || '',
          status: data.status || 'active',
          location: data.location || '',
          assignedTo: data.assignedTo || '',
          purchaseDate: data.purchaseDate ? data.purchaseDate.split('T')[0] : '',
          warrantyUntil: data.warrantyUntil ? data.warrantyUntil.split('T')[0] : '',
          notes: data.notes || ''
        })
      } else {
        setError('Zařízení nebylo nalezeno')
      }
    } catch (error) {
      setError('Chyba při načítání zařízení')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)
    setError('')

    try {
      const response = await fetch(`/api/devices/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        setDevice(data)
        setIsEditing(false)
        router.refresh()
      } else {
        setError(data.error || 'Chyba při ukládání zařízení')
      }
    } catch (err) {
      setError('Došlo k neočekávané chybě')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Načítám zařízení...</p>
        </div>
      </div>
    )
  }

  if (!device) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/devices">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Zpět
            </Button>
          </Link>
        </div>
        <Card>
          <CardContent className="p-12 text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Zařízení nebylo nalezeno</h3>
            <p className="text-gray-600">Požadované zařízení neexistuje nebo bylo smazáno.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/devices">
            <Button variant="outline" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Zpět
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{device.name}</h1>
            <p className="text-gray-600">Evidence číslo: {device.evidenceNumber}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge className={statusColors[device.status]}>
            {statusLabels[device.status]}
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.open(`/api/devices/${device.id}/qr`, '_blank')}
          >
            <QrCode className="h-4 w-4 mr-2" />
            QR kód
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              const qrUrl = `/api/devices/${device.id}/qr`
              const printWindow = window.open(qrUrl, '_blank')
              if (printWindow) {
                printWindow.onload = () => {
                  printWindow.print()
                }
              }
            }}
          >
            <Printer className="h-4 w-4 mr-2" />
            Tisknout QR
          </Button>
          {!isEditing && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(true)}
            >
              <Edit className="h-4 w-4 mr-2" />
              Upravit
            </Button>
          )}
        </div>
      </div>

      {/* Device Details */}
      <Card className="max-w-2xl">
        <CardHeader>
          <CardTitle>{isEditing ? 'Upravit zařízení' : 'Detail zařízení'}</CardTitle>
          <CardDescription>
            {isEditing ? 'Upravte informace o zařízení' : 'Kompletní informace o zařízení'}
          </CardDescription>
        </CardHeader>
        
        <CardContent>
          {isEditing ? (
            <form onSubmit={handleSubmit} className="space-y-6">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Název zařízení *</Label>
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="type">Typ zařízení *</Label>
                  <select
                    id="type"
                    name="type"
                    value={formData.type}
                    onChange={handleInputChange}
                    required
                    className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="laptop">Notebook</option>
                    <option value="desktop">Stolní počítač</option>
                    <option value="server">Server</option>
                    <option value="mobile">Mobilní telefon</option>
                    <option value="tablet">Tablet</option>
                    <option value="other">Ostatní</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="brand">Značka *</Label>
                  <Input
                    id="brand"
                    name="brand"
                    type="text"
                    value={formData.brand}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="model">Model *</Label>
                  <Input
                    id="model"
                    name="model"
                    type="text"
                    value={formData.model}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="serialNumber">Sériové číslo</Label>
                  <Input
                    id="serialNumber"
                    name="serialNumber"
                    type="text"
                    value={formData.serialNumber}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status">Stav *</Label>
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    required
                    className="w-full h-10 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="active">Aktivní</option>
                    <option value="inactive">Neaktivní</option>
                    <option value="maintenance">Údržba</option>
                    <option value="retired">Vyřazeno</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="location">Umístění *</Label>
                  <Input
                    id="location"
                    name="location"
                    type="text"
                    value={formData.location}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="assignedTo">Přiřazeno uživateli</Label>
                  <Input
                    id="assignedTo"
                    name="assignedTo"
                    type="text"
                    value={formData.assignedTo}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="purchaseDate">Datum nákupu</Label>
                  <Input
                    id="purchaseDate"
                    name="purchaseDate"
                    type="date"
                    value={formData.purchaseDate}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="warrantyUntil">Záruka do</Label>
                  <Input
                    id="warrantyUntil"
                    name="warrantyUntil"
                    type="date"
                    value={formData.warrantyUntil}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Poznámky</Label>
                <Textarea
                  id="notes"
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>

              <div className="flex items-center justify-end space-x-4 pt-6 border-t">
                <Button
                  variant="outline"
                  type="button"
                  onClick={() => setIsEditing(false)}
                >
                  Zrušit
                </Button>
                <Button type="submit" disabled={saving} className="bg-blue-600 hover:bg-blue-700">
                  <Save className="h-4 w-4 mr-2" />
                  {saving ? 'Ukládám...' : 'Uložit změny'}
                </Button>
              </div>
            </form>
          ) : (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Název</h4>
                    <p className="text-gray-900">{device.name}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Typ</h4>
                    <p className="text-gray-900">{device.type}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Značka</h4>
                    <p className="text-gray-900">{device.brand}</p>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Model</h4>
                    <p className="text-gray-900">{device.model}</p>
                  </div>
                  {device.serialNumber && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Sériové číslo</h4>
                      <p className="text-gray-900">{device.serialNumber}</p>
                    </div>
                  )}
                </div>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Umístění</h4>
                    <p className="text-gray-900">{device.location}</p>
                  </div>
                  {device.assignedTo && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Přiřazeno</h4>
                      <p className="text-gray-900">{device.assignedTo}</p>
                    </div>
                  )}
                  {device.purchaseDate && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Datum nákupu</h4>
                      <p className="text-gray-900">{new Date(device.purchaseDate).toLocaleDateString('cs-CZ')}</p>
                    </div>
                  )}
                  {device.warrantyUntil && (
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Záruka do</h4>
                      <p className="text-gray-900">{new Date(device.warrantyUntil).toLocaleDateString('cs-CZ')}</p>
                    </div>
                  )}
                </div>
              </div>
              
              {device.notes && (
                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-1">Poznámky</h4>
                  <p className="text-gray-900 whitespace-pre-wrap">{device.notes}</p>
                </div>
              )}
              
              <div className="pt-6 border-t">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-500">
                  <div>
                    <span className="font-medium">Vytvořeno:</span> {new Date(device.createdAt).toLocaleString('cs-CZ')}
                  </div>
                  <div>
                    <span className="font-medium">Upraveno:</span> {new Date(device.updatedAt).toLocaleString('cs-CZ')}
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Files Section */}
      <Card>
        <CardHeader>
          <CardTitle>Soubory</CardTitle>
          <CardDescription>
            Dokumenty a soubory související s tímto zařízením
          </CardDescription>
        </CardHeader>
        <CardContent>
          <FileUpload deviceId={device.id} />
        </CardContent>
      </Card>
    </div>
  )
}
