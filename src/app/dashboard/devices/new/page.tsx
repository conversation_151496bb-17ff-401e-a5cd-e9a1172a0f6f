'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ArrowLeft, Save } from 'lucide-react'
import Link from 'next/link'

interface Organization {
  id: string
  name: string
  slug: string
  role: string
}

export default function NewDevicePage() {
  const router = useRouter()
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [formData, setFormData] = useState({
    organizationId: '',
    evidenceNumber: '',
    name: '',
    description: '',
    deviceType: '',
    manufacturer: '',
    model: '',
    serialNumber: '',
    location: '',
    status: 'aktivni',
    purchaseDate: '',
    warrantyExpiry: '',
    notes: ''
  })

  useEffect(() => {
    fetchUserData()
  }, [])

  const fetchUserData = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setOrganizations(data.user.organizations)
        if (data.user.organizations.length > 0) {
          setFormData(prev => ({
            ...prev,
            organizationId: data.user.organizations[0].id
          }))
        }
      } else {
        router.push('/auth/login')
      }
    } catch (error) {
      console.error('Chyba při načítání uživatele:', error)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/devices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        router.push('/dashboard/devices')
      } else {
        setError(data.error || 'Chyba při vytváření zařízení')
      }
    } catch (err) {
      setError('Došlo k neočekávané chybě')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6 p-4 sm:p-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/devices">
          <Button variant="outline" size="sm" className="rounded-xl touch-manipulation active:scale-95">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Zpět na seznam
          </Button>
        </Link>
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Přidat nové zařízení</h1>
          <p className="text-gray-600 text-sm sm:text-base">Vytvořte nový záznam v evidenci</p>
        </div>
      </div>

      {/* Form */}
      <Card className="shadow-orange-lg border-0 bg-white/80 backdrop-blur-sm">
        <CardHeader className="p-4 sm:p-6">
          <CardTitle className="text-lg sm:text-xl">Základní informace</CardTitle>
          <CardDescription className="text-sm sm:text-base">
            Vyplňte základní údaje o zařízení
          </CardDescription>
        </CardHeader>
        <CardContent className="p-4 sm:p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <Alert variant="destructive" className="border-red-200 bg-red-50">
                <AlertDescription className="text-red-800">{error}</AlertDescription>
              </Alert>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              <div className="space-y-2">
                <Label htmlFor="organizationId" className="text-sm font-medium text-gray-700">
                  Organizace
                </Label>
                <select
                  id="organizationId"
                  name="organizationId"
                  value={formData.organizationId}
                  onChange={handleInputChange}
                  required
                  className="w-full h-12 px-3 border border-gray-200 rounded-xl focus:border-orange-500 focus:ring-orange-500 transition-all duration-200 text-base sm:text-sm touch-manipulation"
                >
                  <option value="">Vyberte organizaci</option>
                  {organizations.map((org) => (
                    <option key={org.id} value={org.id}>
                      {org.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="evidenceNumber" className="text-sm font-medium text-gray-700">
                  Evidenční číslo *
                </Label>
                <Input
                  id="evidenceNumber"
                  name="evidenceNumber"
                  type="text"
                  placeholder="EV-001"
                  value={formData.evidenceNumber}
                  onChange={handleInputChange}
                  required
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200 text-base sm:text-sm"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                  Název zařízení *
                </Label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  placeholder="Dell Latitude 5520"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="deviceType" className="text-sm font-medium text-gray-700">
                  Typ zařízení
                </Label>
                <select
                  id="deviceType"
                  name="deviceType"
                  value={formData.deviceType}
                  onChange={handleInputChange}
                  className="w-full h-12 px-3 border border-gray-200 rounded-xl focus:border-orange-500 focus:ring-orange-500 transition-all duration-200"
                >
                  <option value="">Vyberte typ</option>
                  <option value="laptop">Notebook</option>
                  <option value="desktop">Stolní počítač</option>
                  <option value="server">Server</option>
                  <option value="mobile">Mobilní telefon</option>
                  <option value="tablet">Tablet</option>
                  <option value="monitor">Monitor</option>
                  <option value="printer">Tiskárna</option>
                  <option value="other">Ostatní</option>
                </select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="manufacturer" className="text-sm font-medium text-gray-700">
                  Výrobce
                </Label>
                <Input
                  id="manufacturer"
                  name="manufacturer"
                  type="text"
                  placeholder="Dell"
                  value={formData.manufacturer}
                  onChange={handleInputChange}
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="model" className="text-sm font-medium text-gray-700">
                  Model
                </Label>
                <Input
                  id="model"
                  name="model"
                  type="text"
                  placeholder="Latitude 5520"
                  value={formData.model}
                  onChange={handleInputChange}
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="serialNumber" className="text-sm font-medium text-gray-700">
                  Sériové číslo
                </Label>
                <Input
                  id="serialNumber"
                  name="serialNumber"
                  type="text"
                  placeholder="ABC123456789"
                  value={formData.serialNumber}
                  onChange={handleInputChange}
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="location" className="text-sm font-medium text-gray-700">
                  Umístění
                </Label>
                <Input
                  id="location"
                  name="location"
                  type="text"
                  placeholder="Kancelář 101"
                  value={formData.location}
                  onChange={handleInputChange}
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="status" className="text-sm font-medium text-gray-700">
                  Stav
                </Label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleInputChange}
                  className="w-full h-12 px-3 border border-gray-200 rounded-xl focus:border-orange-500 focus:ring-orange-500 transition-all duration-200"
                >
                  <option value="aktivni">Aktivní</option>
                  <option value="zapujceno">Zapůjčeno</option>
                  <option value="servis">Servis</option>
                  <option value="vyrazeno">Vyřazeno</option>
                </select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="purchaseDate" className="text-sm font-medium text-gray-700">
                  Datum nákupu
                </Label>
                <Input
                  id="purchaseDate"
                  name="purchaseDate"
                  type="date"
                  value={formData.purchaseDate}
                  onChange={handleInputChange}
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="warrantyExpiry" className="text-sm font-medium text-gray-700">
                  Konec záruky
                </Label>
                <Input
                  id="warrantyExpiry"
                  name="warrantyExpiry"
                  type="date"
                  value={formData.warrantyExpiry}
                  onChange={handleInputChange}
                  className="h-12 border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium text-gray-700">
                Popis
              </Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Stručný popis zařízení..."
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes" className="text-sm font-medium text-gray-700">
                Poznámky
              </Label>
              <Textarea
                id="notes"
                name="notes"
                placeholder="Dodatečné poznámky..."
                value={formData.notes}
                onChange={handleInputChange}
                rows={3}
                className="border-gray-200 focus:border-orange-500 focus:ring-orange-500 rounded-xl transition-all duration-200"
              />
            </div>

            <div className="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-4 sm:justify-end pt-6">
              <Link href="/dashboard/devices" className="w-full sm:w-auto">
                <Button variant="outline" className="w-full sm:w-auto rounded-xl touch-manipulation active:scale-95">
                  Zrušit
                </Button>
              </Link>
              <Button
                type="submit"
                disabled={loading}
                className="w-full sm:w-auto gradient-orange hover:shadow-orange text-white rounded-xl transition-all duration-200 transform hover:scale-105 touch-manipulation active:scale-95"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Ukládám...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Uložit zařízení
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
