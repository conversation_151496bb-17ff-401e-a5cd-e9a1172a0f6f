'use client'

import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useOrganization } from '@/hooks/useOrganization'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Calendar, Search, Filter, Download, Eye, Edit, Trash2, Plus, User, Shield } from 'lucide-react'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { format } from 'date-fns'
import { cs } from 'date-fns/locale'

interface AuditLog {
  id: string
  userId: string | null
  action: string
  resource: string
  resourceId: string | null
  details: Record<string, any> | null
  ipAddress: string | null
  userAgent: string | null
  organizationId: string | null
  createdAt: string
  user: {
    id: string
    name: string | null
    email: string
  } | null
}

interface AuditStats {
  totalLogs: number
  todayLogs: number
  weekLogs: number
  topUsers: Array<{
    userId: string
    _count: number
  }>
  topResources: Array<{
    resource: string
    _count: number
  }>
  topActions: Array<{
    action: string
    _count: number
  }>
}

export default function AuditPage() {
  const { currentOrganization } = useOrganization()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedResource, setSelectedResource] = useState<string>('')
  const [selectedAction, setSelectedAction] = useState<string>('')
  const [selectedUser, setSelectedUser] = useState<string>('')
  const [startDate, setStartDate] = useState<string>('')
  const [endDate, setEndDate] = useState<string>('')
  const [page, setPage] = useState(0)
  const limit = 50

  // Build query parameters
  const queryParams = new URLSearchParams()
  if (currentOrganization?.id) queryParams.set('organizationId', currentOrganization.id)
  if (selectedResource) queryParams.set('resource', selectedResource)
  if (selectedAction) queryParams.set('action', selectedAction)
  if (selectedUser) queryParams.set('userId', selectedUser)
  if (startDate) queryParams.set('startDate', startDate)
  if (endDate) queryParams.set('endDate', endDate)
  queryParams.set('limit', limit.toString())
  queryParams.set('offset', (page * limit).toString())

  // Fetch audit logs
  const { data: auditData, isLoading: logsLoading } = useQuery({
    queryKey: ['audit-logs', queryParams.toString()],
    queryFn: async () => {
      const response = await fetch(`/api/audit?${queryParams}`)
      if (!response.ok) throw new Error('Failed to fetch audit logs')
      return response.json()
    },
    enabled: !!currentOrganization?.id,
  })

  // Fetch audit stats
  const { data: stats } = useQuery({
    queryKey: ['audit-stats', currentOrganization?.id],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (currentOrganization?.id) params.set('organizationId', currentOrganization.id)
      const response = await fetch(`/api/audit/stats?${params}`)
      if (!response.ok) throw new Error('Failed to fetch audit stats')
      return response.json()
    },
    enabled: !!currentOrganization?.id,
  })

  const getActionIcon = (action: string) => {
    switch (action.toLowerCase()) {
      case 'create': return <Plus className="h-4 w-4 text-green-600" />
      case 'update': return <Edit className="h-4 w-4 text-blue-600" />
      case 'delete': return <Trash2 className="h-4 w-4 text-red-600" />
      case 'view': return <Eye className="h-4 w-4 text-gray-600" />
      case 'login': return <User className="h-4 w-4 text-green-600" />
      case 'logout': return <User className="h-4 w-4 text-gray-600" />
      default: return <Shield className="h-4 w-4 text-orange-600" />
    }
  }

  const getActionBadgeVariant = (action: string) => {
    switch (action.toLowerCase()) {
      case 'create': return 'default'
      case 'update': return 'secondary'
      case 'delete': return 'destructive'
      case 'view': return 'outline'
      default: return 'outline'
    }
  }

  const clearFilters = () => {
    setSelectedResource('')
    setSelectedAction('')
    setSelectedUser('')
    setStartDate('')
    setEndDate('')
    setPage(0)
  }

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Vyberte organizaci pro zobrazení audit logu</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Audit Log</h1>
          <p className="text-muted-foreground">
            Sledování všech aktivit v organizaci {currentOrganization.name}
          </p>
        </div>
        <Button variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
      </div>

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Celkem záznamů</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalLogs.toLocaleString()}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Dnes</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.todayLogs.toLocaleString()}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tento týden</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.weekLogs.toLocaleString()}</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Filtry
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            <div>
              <Label htmlFor="resource">Zdroj</Label>
              <Select value={selectedResource} onValueChange={setSelectedResource}>
                <SelectTrigger>
                  <SelectValue placeholder="Všechny zdroje" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Všechny zdroje</SelectItem>
                  <SelectItem value="device">Zařízení</SelectItem>
                  <SelectItem value="user">Uživatelé</SelectItem>
                  <SelectItem value="organization">Organizace</SelectItem>
                  <SelectItem value="team">Týmy</SelectItem>
                  <SelectItem value="department">Oddělení</SelectItem>
                  <SelectItem value="auth">Autentizace</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="action">Akce</Label>
              <Select value={selectedAction} onValueChange={setSelectedAction}>
                <SelectTrigger>
                  <SelectValue placeholder="Všechny akce" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Všechny akce</SelectItem>
                  <SelectItem value="CREATE">Vytvoření</SelectItem>
                  <SelectItem value="UPDATE">Úprava</SelectItem>
                  <SelectItem value="DELETE">Smazání</SelectItem>
                  <SelectItem value="VIEW">Zobrazení</SelectItem>
                  <SelectItem value="LOGIN">Přihlášení</SelectItem>
                  <SelectItem value="LOGOUT">Odhlášení</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="startDate">Od</Label>
              <Input
                id="startDate"
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="endDate">Do</Label>
              <Input
                id="endDate"
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
              />
            </div>
            <div className="flex items-end">
              <Button variant="outline" onClick={clearFilters} className="w-full">
                Vymazat filtry
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Audit Logs */}
      {logsLoading ? (
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner className="h-8 w-8" />
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Záznamy aktivit</CardTitle>
            <CardDescription>
              {auditData?.total ? `Zobrazeno ${auditData.logs.length} z ${auditData.total} záznamů` : 'Žádné záznamy'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {auditData?.logs?.map((log: AuditLog) => (
                <div key={log.id} className="flex items-start space-x-4 p-4 border rounded-lg">
                  <div className="flex-shrink-0">
                    {getActionIcon(log.action)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <Badge variant={getActionBadgeVariant(log.action)}>
                        {log.action}
                      </Badge>
                      <Badge variant="outline">
                        {log.resource}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {format(new Date(log.createdAt), 'dd.MM.yyyy HH:mm:ss', { locale: cs })}
                      </span>
                    </div>
                    <div className="mt-1">
                      <span className="text-sm font-medium">
                        {log.user ? `${log.user.name || log.user.email}` : 'Systém'}
                      </span>
                      {log.resourceId && (
                        <span className="text-sm text-muted-foreground ml-2">
                          • ID: {log.resourceId}
                        </span>
                      )}
                    </div>
                    {log.details && (
                      <div className="mt-2 text-sm text-muted-foreground">
                        <pre className="whitespace-pre-wrap font-mono text-xs bg-muted p-2 rounded">
                          {JSON.stringify(log.details, null, 2)}
                        </pre>
                      </div>
                    )}
                    {log.ipAddress && (
                      <div className="mt-1 text-xs text-muted-foreground">
                        IP: {log.ipAddress}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            {auditData?.hasMore && (
              <div className="flex justify-center mt-6">
                <Button
                  variant="outline"
                  onClick={() => setPage(page + 1)}
                  disabled={logsLoading}
                >
                  Načíst další
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
