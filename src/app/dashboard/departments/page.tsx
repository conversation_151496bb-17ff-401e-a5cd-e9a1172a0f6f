'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useOrganization } from '@/hooks/useOrganization'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { toast } from '@/hooks/use-toast'
import { Plus, Search, Edit, Trash2, Building, Users } from 'lucide-react'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

interface Department {
  id: string
  name: string
  description: string | null
  organizationId: string
  createdAt: string
  teams: Array<{
    id: string
    name: string
    _count: {
      members: number
    }
  }>
  _count: {
    teams: number
  }
}

export default function DepartmentsPage() {
  const { currentOrganization } = useOrganization()
  const queryClient = useQueryClient()
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null)
  const [newDepartment, setNewDepartment] = useState({
    name: '',
    description: '',
  })

  // Fetch departments
  const { data: departments, isLoading: departmentsLoading } = useQuery({
    queryKey: ['departments', currentOrganization?.id],
    queryFn: async () => {
      if (!currentOrganization?.id) return []
      const response = await fetch(`/api/departments?organizationId=${currentOrganization.id}`)
      if (!response.ok) throw new Error('Failed to fetch departments')
      return response.json()
    },
    enabled: !!currentOrganization?.id,
  })

  // Create department mutation
  const createDepartmentMutation = useMutation({
    mutationFn: async (departmentData: typeof newDepartment) => {
      const response = await fetch(`/api/departments?organizationId=${currentOrganization?.id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(departmentData),
      })
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create department')
      }
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] })
      setIsCreateDialogOpen(false)
      setNewDepartment({ name: '', description: '' })
      toast({
        title: 'Úspěch',
        description: 'Oddělení bylo úspěšně vytvořeno',
      })
    },
    onError: (error: Error) => {
      toast({
        title: 'Chyba',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  // Update department mutation
  const updateDepartmentMutation = useMutation({
    mutationFn: async ({ departmentId, data }: { departmentId: string; data: Partial<typeof newDepartment> }) => {
      const response = await fetch(`/api/departments/${departmentId}?organizationId=${currentOrganization?.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      })
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update department')
      }
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] })
      setIsEditDialogOpen(false)
      setSelectedDepartment(null)
      toast({
        title: 'Úspěch',
        description: 'Oddělení bylo aktualizováno',
      })
    },
    onError: (error: Error) => {
      toast({
        title: 'Chyba',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  // Delete department mutation
  const deleteDepartmentMutation = useMutation({
    mutationFn: async (departmentId: string) => {
      const response = await fetch(`/api/departments/${departmentId}?organizationId=${currentOrganization?.id}`, {
        method: 'DELETE',
      })
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete department')
      }
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] })
      toast({
        title: 'Úspěch',
        description: 'Oddělení bylo smazáno',
      })
    },
    onError: (error: Error) => {
      toast({
        title: 'Chyba',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  const filteredDepartments = departments?.filter((dept: Department) =>
    dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    dept.description?.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  const handleCreateDepartment = () => {
    createDepartmentMutation.mutate(newDepartment)
  }

  const handleUpdateDepartment = (data: Partial<typeof newDepartment>) => {
    if (selectedDepartment) {
      updateDepartmentMutation.mutate({ departmentId: selectedDepartment.id, data })
    }
  }

  const handleDeleteDepartment = (departmentId: string) => {
    if (confirm('Opravdu chcete smazat toto oddělení? Všechny týmy v tomto oddělení budou přesunuty do kategorie "Bez oddělení".')) {
      deleteDepartmentMutation.mutate(departmentId)
    }
  }

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Vyberte organizaci pro zobrazení oddělení</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Správa oddělení</h1>
          <p className="text-muted-foreground">
            Spravujte oddělení v organizaci {currentOrganization.name}
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Vytvořit oddělení
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Vytvořit nové oddělení</DialogTitle>
              <DialogDescription>
                Vytvořte nové oddělení pro organizaci týmů
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Název oddělení</Label>
                <Input
                  id="name"
                  value={newDepartment.name}
                  onChange={(e) => setNewDepartment({ ...newDepartment, name: e.target.value })}
                  placeholder="IT oddělení"
                />
              </div>
              <div>
                <Label htmlFor="description">Popis</Label>
                <Textarea
                  id="description"
                  value={newDepartment.description}
                  onChange={(e) => setNewDepartment({ ...newDepartment, description: e.target.value })}
                  placeholder="Popis oddělení a jeho odpovědností"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Zrušit
                </Button>
                <Button 
                  onClick={handleCreateDepartment}
                  disabled={createDepartmentMutation.isPending || !newDepartment.name}
                >
                  {createDepartmentMutation.isPending && <LoadingSpinner className="mr-2 h-4 w-4" />}
                  Vytvořit
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Hledat oddělení..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {/* Departments List */}
      {departmentsLoading ? (
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner className="h-8 w-8" />
        </div>
      ) : (
        <div className="grid gap-4">
          {filteredDepartments.map((department: Department) => (
            <Card key={department.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="h-10 w-10 rounded-full bg-purple-100 flex items-center justify-center">
                      <Building className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold">{department.name}</h3>
                      <p className="text-sm text-muted-foreground">{department.description}</p>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                        <span className="flex items-center">
                          <Users className="h-3 w-3 mr-1" />
                          {department._count.teams} týmů
                        </span>
                        <span>
                          {department.teams.reduce((total, team) => total + team._count.members, 0)} členů
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedDepartment(department)
                        setIsEditDialogOpen(true)
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteDepartment(department.id)}
                      disabled={department._count.teams > 0}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                {/* Teams in Department */}
                {department.teams.length > 0 && (
                  <div className="mt-4 pt-4 border-t">
                    <h4 className="text-sm font-medium mb-2">Týmy v oddělení:</h4>
                    <div className="flex flex-wrap gap-2">
                      {department.teams.map((team) => (
                        <Badge key={team.id} variant="outline">
                          {team.name} ({team._count.members})
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Edit Department Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upravit oddělení</DialogTitle>
            <DialogDescription>
              Upravte informace o oddělení {selectedDepartment?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="editName">Název oddělení</Label>
              <Input
                id="editName"
                defaultValue={selectedDepartment?.name}
                onChange={(e) => handleUpdateDepartment({ name: e.target.value })}
                placeholder="IT oddělení"
              />
            </div>
            <div>
              <Label htmlFor="editDescription">Popis</Label>
              <Textarea
                id="editDescription"
                defaultValue={selectedDepartment?.description || ''}
                onChange={(e) => handleUpdateDepartment({ description: e.target.value })}
                placeholder="Popis oddělení a jeho odpovědností"
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
