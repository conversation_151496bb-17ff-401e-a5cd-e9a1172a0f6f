'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter } from 'next/navigation'
import { useQuery } from '@tanstack/react-query'
import { PageLoadingSpinner } from '@/components/ui/loading-spinner'
import { PullToRefresh } from '@/components/ui/pull-to-refresh'
import { StatsCards } from '@/components/dashboard/stats-cards'
import { Charts } from '@/components/dashboard/charts'
import { RecentActivity } from '@/components/dashboard/recent-activity'
import { QuickActions } from '@/components/dashboard/quick-actions'
import { queryKeys } from '@/lib/react-query'
import { useToast } from '@/hooks/use-toast'

export default function DashboardPage() {
  const router = useRouter()
  const [mounted, setMounted] = useState(false)
  const [userInfo, setUserInfo] = useState<any>(null)
  const { success, error } = useToast()

  useEffect(() => {
    setMounted(true)
    // Get user info from session
    fetch('/api/auth/me')
      .then(res => res.json())
      .then(data => setUserInfo(data.user))
      .catch(console.error)
  }, [])

  // Fetch dashboard stats with React Query
  const { data: stats, isLoading: statsLoading, error: statsError, refetch: refetchStats } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/stats')
      if (!response.ok) {
        throw new Error('Chyba při načítání statistik')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: mounted,
  })

  // Fetch dashboard activities with React Query
  const { data: activities, isLoading: activitiesLoading, error: activitiesError, refetch: refetchActivities } = useQuery({
    queryKey: ['dashboard-activities'],
    queryFn: async () => {
      const response = await fetch('/api/dashboard/activities')
      if (!response.ok) {
        throw new Error('Chyba při načítání aktivit')
      }
      return response.json()
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: mounted,
  })
  const loading = statsLoading || activitiesLoading

  // Refresh function for pull-to-refresh
  const handleRefresh = async () => {
    await Promise.all([
      refetchStats(),
      refetchActivities()
    ])
    success('Data byla obnovena')
  }

  // Handle errors
  useEffect(() => {
    if (statsError) {
      console.error('Stats error:', statsError)
      error('Chyba při načítání statistik', 'Zkuste obnovit stránku')
    }
    if (activitiesError) {
      console.error('Activities error:', activitiesError)
      error('Chyba při načítání aktivit', 'Zkuste obnovit stránku')
    }
  }, [statsError, activitiesError, error])

  if (!mounted) {
    return <PageLoadingSpinner />
  }

  if (loading) {
    return (
      <div className="space-y-6 p-4 sm:p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="h-64 bg-gray-200 rounded"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <PullToRefresh onRefresh={handleRefresh} className="min-h-screen">
      <div className="space-y-6 p-4 sm:p-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Přehled vašeho systému</p>
        </div>

        {/* Stats Overview */}
        {stats?.overview && (
          <StatsCards stats={stats.overview} />
        )}

        {/* Quick Actions */}
        {userInfo && (
          <QuickActions
            userRole={userInfo.organizations?.[0]?.role || 'ctenar'}
            isSuperAdmin={userInfo.isSuperAdmin || false}
            stats={stats?.overview}
          />
        )}

        {/* Charts */}
        {stats?.charts && (
          <Charts charts={stats.charts} />
        )}

        {/* Recent Activity */}
        {activities?.activities && (
          <RecentActivity activities={activities.activities} />
        )}
      </div>
    </PullToRefresh>
  )
}
