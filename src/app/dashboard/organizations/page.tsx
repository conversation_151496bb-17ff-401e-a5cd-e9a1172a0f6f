'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Building2, Users, Settings, Crown, Wrench, Eye } from 'lucide-react'

interface Organization {
  id: string
  name: string
  slug: string
  role: string
  memberCount?: number
  deviceCount?: number
}

const roleIcons = {
  admin: Crown,
  technik: Wrench,
  ctenar: Eye
}

const roleLabels = {
  admin: 'Administrátor',
  technik: 'Technik',
  ctenar: 'Čtenář'
}

const roleColors = {
  admin: 'bg-purple-100 text-purple-800 border-purple-200',
  technik: 'bg-blue-100 text-blue-800 border-blue-200',
  ctenar: 'bg-gray-100 text-gray-800 border-gray-200'
}

export default function OrganizationsPage() {
  const router = useRouter()
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchOrganizations()
  }, [])

  const fetchOrganizations = async () => {
    try {
      const response = await fetch('/api/auth/me')
      if (response.ok) {
        const data = await response.json()
        setOrganizations(data.user.organizations)
      } else {
        router.push('/auth/login')
      }
    } catch (error) {
      console.error('Chyba při načítání organizací:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Načítám organizace...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Organizace</h1>
          <p className="text-gray-600">Správa organizací a členství</p>
        </div>
      </div>

      {/* Organizations Grid */}
      {organizations.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Nejste členem žádné organizace
            </h3>
            <p className="text-gray-600 mb-6">
              Požádejte administrátora o přidání do organizace nebo si vytvořte novou
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {organizations.map((org) => {
            const RoleIcon = roleIcons[org.role as keyof typeof roleIcons] || Building2
            
            return (
              <Card key={org.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-orange-100 rounded-lg">
                        <Building2 className="h-5 w-5 text-orange-600" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">{org.name}</CardTitle>
                        <CardDescription className="text-sm">
                          {org.slug}
                        </CardDescription>
                      </div>
                    </div>
                    <Badge className={roleColors[org.role as keyof typeof roleColors]}>
                      <RoleIcon className="h-3 w-3 mr-1" />
                      {roleLabels[org.role as keyof typeof roleLabels]}
                    </Badge>
                  </div>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <Users className="h-4 w-4 text-gray-600 mx-auto mb-1" />
                      <div className="font-medium text-gray-900">{org.memberCount || '?'}</div>
                      <div className="text-gray-600 text-xs">Členové</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <Building2 className="h-4 w-4 text-gray-600 mx-auto mb-1" />
                      <div className="font-medium text-gray-900">{org.deviceCount || '?'}</div>
                      <div className="text-gray-600 text-xs">Zařízení</div>
                    </div>
                  </div>
                  
                  <div className="flex justify-between pt-3 border-t">
                    <div className="text-xs text-gray-500">
                      Vaše role: {roleLabels[org.role as keyof typeof roleLabels]}
                    </div>
                    {org.role === 'admin' && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}

      {/* Info Card */}
      <Card className="bg-orange-50 border-orange-200">
        <CardContent className="p-6">
          <div className="flex items-start space-x-3">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Building2 className="h-5 w-5 text-orange-600" />
            </div>
            <div>
              <h3 className="font-semibold text-orange-900 mb-1">O organizacích</h3>
              <p className="text-orange-800 text-sm mb-3">
                Organizace umožňují sdílení zařízení a spolupráci v týmu. Každá organizace má své vlastní zařízení a členy s různými oprávněními.
              </p>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Crown className="h-4 w-4 text-purple-600" />
                  <span className="text-orange-800"><strong>Administrátor:</strong> Plná správa organizace a všech zařízení</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Wrench className="h-4 w-4 text-blue-600" />
                  <span className="text-orange-800"><strong>Technik:</strong> Správa zařízení, nemůže spravovat členy</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Eye className="h-4 w-4 text-gray-600" />
                  <span className="text-orange-800"><strong>Čtenář:</strong> Pouze prohlížení zařízení</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
