'use client'

import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { useOrganization } from '@/hooks/useOrganization'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { toast } from '@/hooks/use-toast'
import { Plus, Search, Edit, Trash2, Users, Building, UserPlus, UserMinus } from 'lucide-react'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

interface Team {
  id: string
  name: string
  description: string | null
  departmentId: string | null
  leaderId: string | null
  organizationId: string
  createdAt: string
  department: {
    id: string
    name: string
  } | null
  leader: {
    id: string
    name: string | null
    email: string
  } | null
  members: Array<{
    id: string
    userId: string
    user: {
      id: string
      name: string | null
      email: string
    }
  }>
  _count: {
    members: number
  }
}

interface Department {
  id: string
  name: string
  description: string | null
}

interface User {
  id: string
  name: string | null
  email: string
}

export default function TeamsPage() {
  const { currentOrganization } = useOrganization()
  const queryClient = useQueryClient()
  const [searchTerm, setSearchTerm] = useState('')
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isMembersDialogOpen, setIsMembersDialogOpen] = useState(false)
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null)
  const [newTeam, setNewTeam] = useState({
    name: '',
    description: '',
    departmentId: '',
    leaderId: '',
  })

  // Fetch teams
  const { data: teams, isLoading: teamsLoading } = useQuery({
    queryKey: ['teams', currentOrganization?.id],
    queryFn: async () => {
      if (!currentOrganization?.id) return []
      const response = await fetch(`/api/teams?organizationId=${currentOrganization.id}`)
      if (!response.ok) throw new Error('Failed to fetch teams')
      return response.json()
    },
    enabled: !!currentOrganization?.id,
  })

  // Fetch departments
  const { data: departments } = useQuery({
    queryKey: ['departments', currentOrganization?.id],
    queryFn: async () => {
      if (!currentOrganization?.id) return []
      const response = await fetch(`/api/departments?organizationId=${currentOrganization.id}`)
      if (!response.ok) throw new Error('Failed to fetch departments')
      return response.json()
    },
    enabled: !!currentOrganization?.id,
  })

  // Fetch users
  const { data: users } = useQuery({
    queryKey: ['users', currentOrganization?.id],
    queryFn: async () => {
      if (!currentOrganization?.id) return []
      const response = await fetch(`/api/users?organizationId=${currentOrganization.id}`)
      if (!response.ok) throw new Error('Failed to fetch users')
      return response.json()
    },
    enabled: !!currentOrganization?.id,
  })

  // Create team mutation
  const createTeamMutation = useMutation({
    mutationFn: async (teamData: typeof newTeam) => {
      const response = await fetch(`/api/teams?organizationId=${currentOrganization?.id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(teamData),
      })
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create team')
      }
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
      setIsCreateDialogOpen(false)
      setNewTeam({ name: '', description: '', departmentId: '', leaderId: '' })
      toast({
        title: 'Úspěch',
        description: 'Tým byl úspěšně vytvořen',
      })
    },
    onError: (error: Error) => {
      toast({
        title: 'Chyba',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  // Update team mutation
  const updateTeamMutation = useMutation({
    mutationFn: async ({ teamId, data }: { teamId: string; data: Partial<typeof newTeam> }) => {
      const response = await fetch(`/api/teams/${teamId}?organizationId=${currentOrganization?.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      })
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update team')
      }
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
      setIsEditDialogOpen(false)
      setSelectedTeam(null)
      toast({
        title: 'Úspěch',
        description: 'Tým byl aktualizován',
      })
    },
    onError: (error: Error) => {
      toast({
        title: 'Chyba',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  // Delete team mutation
  const deleteTeamMutation = useMutation({
    mutationFn: async (teamId: string) => {
      const response = await fetch(`/api/teams/${teamId}?organizationId=${currentOrganization?.id}`, {
        method: 'DELETE',
      })
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to delete team')
      }
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
      toast({
        title: 'Úspěch',
        description: 'Tým byl smazán',
      })
    },
    onError: (error: Error) => {
      toast({
        title: 'Chyba',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  // Add team member mutation
  const addMemberMutation = useMutation({
    mutationFn: async ({ teamId, userId }: { teamId: string; userId: string }) => {
      const response = await fetch(`/api/teams/${teamId}/members?organizationId=${currentOrganization?.id}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId }),
      })
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to add member')
      }
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
      toast({
        title: 'Úspěch',
        description: 'Člen byl přidán do týmu',
      })
    },
    onError: (error: Error) => {
      toast({
        title: 'Chyba',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  // Remove team member mutation
  const removeMemberMutation = useMutation({
    mutationFn: async ({ teamId, userId }: { teamId: string; userId: string }) => {
      const response = await fetch(`/api/teams/${teamId}/members?organizationId=${currentOrganization?.id}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId }),
      })
      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to remove member')
      }
      return response.json()
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['teams'] })
      toast({
        title: 'Úspěch',
        description: 'Člen byl odebrán z týmu',
      })
    },
    onError: (error: Error) => {
      toast({
        title: 'Chyba',
        description: error.message,
        variant: 'destructive',
      })
    },
  })

  const filteredTeams = teams?.filter((team: Team) =>
    team.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    team.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    team.department?.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  const handleCreateTeam = () => {
    createTeamMutation.mutate(newTeam)
  }

  const handleUpdateTeam = (data: Partial<typeof newTeam>) => {
    if (selectedTeam) {
      updateTeamMutation.mutate({ teamId: selectedTeam.id, data })
    }
  }

  const handleDeleteTeam = (teamId: string) => {
    if (confirm('Opravdu chcete smazat tento tým?')) {
      deleteTeamMutation.mutate(teamId)
    }
  }

  const handleAddMember = (userId: string) => {
    if (selectedTeam) {
      addMemberMutation.mutate({ teamId: selectedTeam.id, userId })
    }
  }

  const handleRemoveMember = (userId: string) => {
    if (selectedTeam && confirm('Opravdu chcete odebrat tohoto člena z týmu?')) {
      removeMemberMutation.mutate({ teamId: selectedTeam.id, userId })
    }
  }

  if (!currentOrganization) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Vyberte organizaci pro zobrazení týmů</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Správa týmů</h1>
          <p className="text-muted-foreground">
            Spravujte týmy a jejich členy v organizaci {currentOrganization.name}
          </p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Vytvořit tým
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Vytvořit nový tým</DialogTitle>
              <DialogDescription>
                Vytvořte nový tým a přiřaďte mu vedoucího
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Název týmu</Label>
                <Input
                  id="name"
                  value={newTeam.name}
                  onChange={(e) => setNewTeam({ ...newTeam, name: e.target.value })}
                  placeholder="Vývojový tým"
                />
              </div>
              <div>
                <Label htmlFor="description">Popis</Label>
                <Textarea
                  id="description"
                  value={newTeam.description}
                  onChange={(e) => setNewTeam({ ...newTeam, description: e.target.value })}
                  placeholder="Popis týmu a jeho odpovědností"
                />
              </div>
              <div>
                <Label htmlFor="department">Oddělení</Label>
                <Select value={newTeam.departmentId} onValueChange={(value) => setNewTeam({ ...newTeam, departmentId: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Vyberte oddělení" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Bez oddělení</SelectItem>
                    {departments?.map((dept: Department) => (
                      <SelectItem key={dept.id} value={dept.id}>
                        {dept.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="leader">Vedoucí týmu</Label>
                <Select value={newTeam.leaderId} onValueChange={(value) => setNewTeam({ ...newTeam, leaderId: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Vyberte vedoucího" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Bez vedoucího</SelectItem>
                    {users?.map((user: User) => (
                      <SelectItem key={user.id} value={user.id}>
                        {user.name || user.email}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Zrušit
                </Button>
                <Button 
                  onClick={handleCreateTeam}
                  disabled={createTeamMutation.isPending || !newTeam.name}
                >
                  {createTeamMutation.isPending && <LoadingSpinner className="mr-2 h-4 w-4" />}
                  Vytvořit
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Hledat týmy..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      {/* Teams List */}
      {teamsLoading ? (
        <div className="flex items-center justify-center h-64">
          <LoadingSpinner className="h-8 w-8" />
        </div>
      ) : (
        <div className="grid gap-4">
          {filteredTeams.map((team: Team) => (
            <Card key={team.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
                      <Users className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold">{team.name}</h3>
                        {team.department && (
                          <Badge variant="outline">
                            <Building className="h-3 w-3 mr-1" />
                            {team.department.name}
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">{team.description}</p>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-muted-foreground">
                        <span className="flex items-center">
                          <Users className="h-3 w-3 mr-1" />
                          {team._count.members} členů
                        </span>
                        {team.leader && (
                          <span>
                            Vedoucí: {team.leader.name || team.leader.email}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedTeam(team)
                        setIsMembersDialogOpen(true)
                      }}
                    >
                      <Users className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedTeam(team)
                        setIsEditDialogOpen(true)
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDeleteTeam(team.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Team Members Dialog */}
      <Dialog open={isMembersDialogOpen} onOpenChange={setIsMembersDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Členové týmu {selectedTeam?.name}</DialogTitle>
            <DialogDescription>
              Spravujte členy týmu a jejich role
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {/* Add Member */}
            <div className="flex space-x-2">
              <Select onValueChange={handleAddMember}>
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Přidat člena" />
                </SelectTrigger>
                <SelectContent>
                  {users?.filter((user: User) =>
                    !selectedTeam?.members.some(member => member.userId === user.id)
                  ).map((user: User) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.name || user.email}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Current Members */}
            <div className="space-y-2">
              <h4 className="font-medium">Současní členové:</h4>
              {selectedTeam?.members.map((member) => (
                <div key={member.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                      <Users className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium">{member.user.name || member.user.email}</p>
                      <p className="text-sm text-muted-foreground">{member.user.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {selectedTeam.leaderId === member.userId && (
                      <Badge variant="default">Vedoucí</Badge>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRemoveMember(member.userId)}
                      disabled={selectedTeam.leaderId === member.userId}
                    >
                      <UserMinus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
              {selectedTeam?.members.length === 0 && (
                <p className="text-muted-foreground text-center py-4">
                  Tým zatím nemá žádné členy
                </p>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Team Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upravit tým</DialogTitle>
            <DialogDescription>
              Upravte informace o týmu {selectedTeam?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="editName">Název týmu</Label>
              <Input
                id="editName"
                defaultValue={selectedTeam?.name}
                onChange={(e) => handleUpdateTeam({ name: e.target.value })}
                placeholder="Vývojový tým"
              />
            </div>
            <div>
              <Label htmlFor="editDescription">Popis</Label>
              <Textarea
                id="editDescription"
                defaultValue={selectedTeam?.description || ''}
                onChange={(e) => handleUpdateTeam({ description: e.target.value })}
                placeholder="Popis týmu a jeho odpovědností"
              />
            </div>
            <div>
              <Label htmlFor="editDepartment">Oddělení</Label>
              <Select
                defaultValue={selectedTeam?.departmentId || ''}
                onValueChange={(value) => handleUpdateTeam({ departmentId: value || null })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Vyberte oddělení" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Bez oddělení</SelectItem>
                  {departments?.map((dept: Department) => (
                    <SelectItem key={dept.id} value={dept.id}>
                      {dept.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="editLeader">Vedoucí týmu</Label>
              <Select
                defaultValue={selectedTeam?.leaderId || ''}
                onValueChange={(value) => handleUpdateTeam({ leaderId: value || null })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Vyberte vedoucího" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Bez vedoucího</SelectItem>
                  {users?.map((user: User) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.name || user.email}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
