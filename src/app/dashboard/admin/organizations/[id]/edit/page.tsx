'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, Building2, Shield } from 'lucide-react'
import Link from 'next/link'

interface Organization {
  id: string
  name: string
  slug: string
  description?: string
}

export default function EditOrganizationPage() {
  const router = useRouter()
  const params = useParams()
  const [loading, setLoading] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [error, setError] = useState('')
  const [organization, setOrganization] = useState<Organization | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: ''
  })

  useEffect(() => {
    setMounted(true)
    checkSuperAdmin()
    fetchOrganization()
  }, [])

  const checkSuperAdmin = async () => {
    try {
      const userResponse = await fetch('/api/auth/me')
      if (!userResponse.ok) {
        router.push('/auth/login')
        return
      }
      
      const userData = await userResponse.json()
      if (!userData.user.isSuperAdmin) {
        router.push('/dashboard')
        return
      }
    } catch (error) {
      console.error('Chyba při ověřování oprávnění:', error)
      router.push('/dashboard')
    }
  }

  const fetchOrganization = async () => {
    try {
      const response = await fetch(`/api/admin/organizations/${params.id}`)
      if (response.ok) {
        const data = await response.json()
        setOrganization(data.organization)
        setFormData({
          name: data.organization.name,
          slug: data.organization.slug,
          description: data.organization.description || ''
        })
      } else {
        setError('Organizace nenalezena')
      }
    } catch (error) {
      console.error('Chyba při načítání organizace:', error)
      setError('Chyba při načítání organizace')
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Auto-generate slug from name
    if (name === 'name') {
      const slug = value
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim()
      setFormData(prev => ({
        ...prev,
        slug
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await fetch(`/api/admin/organizations/${params.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        router.push('/dashboard/admin/organizations')
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Chyba při aktualizaci organizace')
      }
    } catch (error) {
      console.error('Chyba při aktualizaci organizace:', error)
      setError('Chyba při aktualizaci organizace')
    } finally {
      setLoading(false)
    }
  }

  if (!mounted || !organization) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Načítám organizaci...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Link href="/dashboard/admin/organizations">
          <Button variant="outline" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Zpět
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Shield className="h-6 w-6 text-orange-600" />
            Upravit organizaci
          </h1>
          <p className="text-gray-600 mt-1">
            Upravte údaje organizace {organization.name}
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building2 className="h-5 w-5 text-orange-600" />
            <span>Základní údaje</span>
          </CardTitle>
          <CardDescription>
            Upravte základní informace o organizaci
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-800 text-sm">{error}</p>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Název organizace *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="např. Moje firma s.r.o."
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">URL slug *</Label>
                <Input
                  id="slug"
                  name="slug"
                  value={formData.slug}
                  onChange={handleInputChange}
                  placeholder="moje-firma"
                  required
                />
                <p className="text-xs text-gray-500">
                  Používá se v URL adresách. Pouze malá písmena, čísla a pomlčky.
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Popis</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Krátký popis organizace..."
                rows={3}
              />
            </div>

            <div className="flex space-x-4 pt-4">
              <Button
                type="submit"
                disabled={loading}
                className="gradient-orange hover:shadow-orange text-white rounded-xl transition-all duration-200 transform hover:scale-105"
              >
                {loading ? 'Ukládá se...' : 'Uložit změny'}
              </Button>
              <Link href="/dashboard/admin/organizations">
                <Button variant="outline" type="button">
                  Zrušit
                </Button>
              </Link>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
