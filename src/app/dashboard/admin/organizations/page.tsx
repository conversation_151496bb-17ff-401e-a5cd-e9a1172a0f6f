'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Plus, Search, Building2, Users, Monitor, Edit, Trash2, Shield } from 'lucide-react'
import Link from 'next/link'

interface Organization {
  id: string
  name: string
  slug: string
  description?: string
  memberCount: number
  deviceCount: number
  activeDevices: number
  adminCount: number
  createdAt: string
}

export default function AdminOrganizationsPage() {
  const router = useRouter()
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    fetchOrganizations()
  }, [])

  const fetchOrganizations = async () => {
    try {
      // First check if user is superadmin
      const userResponse = await fetch('/api/auth/me')
      if (!userResponse.ok) {
        router.push('/auth/login')
        return
      }
      
      const userData = await userResponse.json()
      if (!userData.user.isSuperAdmin) {
        router.push('/dashboard')
        return
      }

      const response = await fetch('/api/admin/organizations')
      if (response.ok) {
        const data = await response.json()
        setOrganizations(data.organizations)
      }
    } catch (error) {
      console.error('Chyba při načítání organizací:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteOrganization = async (orgId: string) => {
    if (!confirm('Opravdu chcete smazat tuto organizaci? Tato akce je nevratná.')) {
      return
    }

    try {
      const response = await fetch(`/api/admin/organizations/${orgId}`, {
        method: 'DELETE'
      })
      
      if (response.ok) {
        setOrganizations(organizations.filter(org => org.id !== orgId))
      } else {
        const error = await response.json()
        alert(error.error || 'Chyba při mazání organizace')
      }
    } catch (error) {
      console.error('Chyba při mazání organizace:', error)
      alert('Chyba při mazání organizace')
    }
  }

  const filteredOrganizations = organizations.filter(org =>
    org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    org.slug.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (!mounted || loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Načítám organizace...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Shield className="h-6 w-6 text-orange-600" />
            Správa organizací
          </h1>
          <p className="text-gray-600 mt-1">
            Spravujte všechny organizace v systému (SuperAdmin)
          </p>
        </div>
        <Link href="/dashboard/admin/organizations/new">
          <Button className="gradient-orange hover:shadow-orange text-white rounded-xl transition-all duration-200 transform hover:scale-105">
            <Plus className="h-4 w-4 mr-2" />
            Přidat organizaci
          </Button>
        </Link>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Hledat podle názvu nebo slug..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Organizations Grid */}
      {filteredOrganizations.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchTerm ? 'Žádné organizace nenalezeny' : 'Zatím nemáte žádné organizace'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchTerm 
                ? 'Zkuste změnit vyhledávací kritéria'
                : 'Začněte přidáním první organizace'
              }
            </p>
            {!searchTerm && (
              <Link href="/dashboard/admin/organizations/new">
                <Button className="gradient-orange hover:shadow-orange text-white rounded-xl transition-all duration-200 transform hover:scale-105">
                  <Plus className="h-4 w-4 mr-2" />
                  Přidat první organizaci
                </Button>
              </Link>
            )}
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredOrganizations.map((org) => (
            <Card key={org.id} className="hover:shadow-lg transition-shadow">
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Building2 className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{org.name}</CardTitle>
                      <CardDescription className="text-sm">
                        /{org.slug}
                      </CardDescription>
                    </div>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Users className="h-4 w-4 text-gray-400" />
                    <span>{org.memberCount} členů</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Monitor className="h-4 w-4 text-gray-400" />
                    <span>{org.deviceCount} zařízení</span>
                  </div>
                </div>

                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">Aktivní zařízení:</span>
                  <Badge className="bg-green-100 text-green-800 border-green-200">
                    {org.activeDevices}
                  </Badge>
                </div>

                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">Administrátoři:</span>
                  <Badge className="bg-blue-100 text-blue-800 border-blue-200">
                    {org.adminCount}
                  </Badge>
                </div>

                <div className="flex space-x-2 pt-2">
                  <Link href={`/dashboard/admin/organizations/${org.id}/edit`} className="flex-1">
                    <Button variant="outline" size="sm" className="w-full">
                      <Edit className="h-4 w-4 mr-2" />
                      Upravit
                    </Button>
                  </Link>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => handleDeleteOrganization(org.id)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
