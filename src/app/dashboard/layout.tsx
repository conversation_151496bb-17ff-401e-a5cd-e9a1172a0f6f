import { requireAuth } from '@/lib/auth'
import { DashboardNav } from '@/components/dashboard/nav'
import { OrganizationSwitcher } from '@/components/dashboard/organization-switcher'
import { MobileNav } from '@/components/dashboard/mobile-nav'
import { PWAInstall } from '@/components/pwa/pwa-install'
import { BottomNavigation, BottomNavigationSpacer } from '@/components/ui/bottom-navigation'
import { DeviceActionsFAB } from '@/components/ui/floating-action-button'
import { prisma } from '@/lib/prisma'

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const user = await requireAuth()

  // Get all organizations for superadmin
  let allOrganizations = null
  if (user.isSuperAdmin) {
    try {
      allOrganizations = await prisma.organization.findMany({
        select: {
          id: true,
          name: true,
          slug: true
        },
        orderBy: {
          name: 'asc'
        }
      })
    } catch (error) {
      console.error('Failed to fetch all organizations:', error)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex h-screen">
        {/* Desktop Sidebar */}
        <div className="hidden md:flex md:w-64 md:flex-col">
          <div className="flex flex-col flex-grow pt-6 overflow-y-auto bg-white border-r border-gray-200 shadow-sm">
            <div className="flex items-center flex-shrink-0 px-6">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <img src="/logo.svg" alt="RokitBox" className="w-8 h-8" />
                </div>
                <h1 className="text-xl font-bold text-gradient-orange">RokitBox</h1>
              </div>
            </div>

            <div className="mt-6 px-4">
              <OrganizationSwitcher
                organizations={user.organizationMembers}
                isSuperAdmin={user.isSuperAdmin}
                allOrganizations={allOrganizations}
              />
            </div>

            <div className="mt-6 flex-grow flex flex-col">
              <DashboardNav isSuperAdmin={user.isSuperAdmin} />
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex flex-col flex-1 overflow-hidden">
          {/* Mobile header */}
          <div className="md:hidden bg-white border-b border-gray-200 px-4 py-3 shadow-sm sticky top-0 z-40">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <img src="/logo.svg" alt="RokitBox" className="w-8 h-8" />
                </div>
                <h1 className="text-xl font-bold text-gradient-orange">RokitBox</h1>
              </div>
              <MobileNav
                organizations={user.organizationMembers}
                isSuperAdmin={user.isSuperAdmin}
                allOrganizations={allOrganizations}
              />
            </div>
          </div>

          <main className="flex-1 relative overflow-y-auto focus:outline-none">
            <div className="min-h-full pb-16 md:pb-0">
              {children}
            </div>
          </main>
        </div>
      </div>

      {/* Mobile Bottom Navigation */}
      <BottomNavigation />

      {/* Mobile Floating Action Button */}
      <DeviceActionsFAB />

      {/* PWA Install Prompt */}
      <PWAInstall />
    </div>
  )
}
