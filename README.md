# RokitBox - Evidenční systém hardwaru

Moderní webová aplikace pro evidenci a správu hardwaru s podporou více organizací, QR kódů a PWA funkcionalitou.

## 🚀 Funkce

### ✅ Implementované funkce
- **Multi-organizační architektura** - Podpora více organizací s izolac<PERSON> dat
- **Uživatelské role** - Admin, Technik, Čtenář s různými oprávněními
- **Správa zařízení** - CRUD operace pro zařízení s evidenční<PERSON> čísly
- **Pokročilé vyhledávání** - Fulltextové vyhledávání a filtrování
- **Historie změn** - Automatické sledování všech změn zařízení
- **Upload souborů** - Nahrávání dokumentů a fotografií k zařízením
- **QR kódy** - Generování a tisk QR kódů pro zařízení
- **Dashboard se statistikami** - <PERSON><PERSON><PERSON><PERSON> stavu zařízení podle lokace a typu
- **Responzivní design** - Optimalizováno pro desktop i mobilní zařízení
- **PWA podpora** - Možnost instalace jako aplikace, offline funkcionalita

### 🔐 Autentizace a autorizace
- Přihlášení pomocí emailu/hesla nebo Google OAuth
- Row Level Security (RLS) pro bezpečnou izolaci dat
- Automatická správa uživatelských profilů

### 📱 Mobilní podpora
- Responzivní design pro všechny velikosti obrazovek
- Mobilní navigace s hamburger menu
- PWA manifest pro instalaci na mobilní zařízení
- Service Worker pro offline funkcionalitu

## 🛠 Technologický stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Real-time)
- **UI komponenty**: Radix UI primitives, Shadcn/ui
- **Formuláře**: React Hook Form + Zod validace
- **QR kódy**: qrcode.react
- **PWA**: Service Worker, Web App Manifest

## 📦 Instalace a spuštění

### Předpoklady
- Node.js 18+
- npm nebo yarn
- Supabase účet

### 1. Klonování repozitáře
```bash
git clone <repository-url>
cd RokitBox
```

### 2. Instalace závislostí
```bash
npm install
```

### 3. Konfigurace prostředí
Vytvořte soubor `.env.local`:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Nastavení databáze
Spusťte migrace v Supabase SQL editoru:
```sql
-- Spusťte soubory v pořadí:
-- 1. supabase/migrations/001_initial_schema.sql
-- 2. supabase/migrations/002_device_history_trigger.sql
-- 3. supabase/migrations/003_storage_setup.sql
```

### 5. Spuštění aplikace
```bash
npm run dev
```

Aplikace bude dostupná na `http://localhost:3000`

### ⚠️ Řešení problémů

**Chyba "Invalid URL" v middleware:**
- Zkontrolujte, že máte správně nastavené environment variables v `.env.local`
- Pokud Supabase není nakonfigurován, aplikace zobrazí setup stránku
- Middleware automaticky detekuje chybějící konfiguraci a umožní přístup k aplikaci

**Chyba při přístupu k databázi:**
- Ověřte, že jste spustili všechny migrace v Supabase
- Zkontrolujte, že máte správné API klíče
- Ujistěte se, že RLS políčky jsou aktivní

## 📊 Databázová struktura

### Hlavní tabulky
- `organizations` - Organizace/firmy
- `profiles` - Uživatelské profily
- `organization_members` - Členství v organizacích s rolemi
- `devices` - Zařízení s evidenčními čísly
- `device_files` - Soubory přiložené k zařízením
- `device_history` - Historie změn zařízení

### Bezpečnost
- Row Level Security (RLS) na všech tabulkách
- Automatická izolace dat podle organizace
- Kontrola oprávnění na úrovni databáze

## 🎯 Použití

### 1. Registrace a přihlášení
- Zaregistrujte se pomocí emailu nebo Google účtu
- Požádejte administrátora o přidání do organizace

### 2. Správa zařízení
- Přidávejte nová zařízení s evidenčními čísly
- Upravujte informace o zařízeních
- Sledujte historii změn
- Nahrávejte dokumenty a fotografie

### 3. QR kódy
- Generujte QR kódy pro rychlý přístup k zařízením
- Tiskněte štítky s QR kódy
- Skenujte QR kódy pro zobrazení detailu

### 4. Dashboard
- Sledujte statistiky zařízení
- Filtrujte podle stavu, lokace nebo typu
- Exportujte data

## 🔧 Konfigurace

### Uživatelské role
- **Admin** - Plná správa organizace a uživatelů
- **Technik** - Správa zařízení, přidávání/úpravy
- **Čtenář** - Pouze čtení informací

### Stavy zařízení
- **Aktivní** - Zařízení v provozu
- **Zapůjčeno** - Dočasně zapůjčeno
- **V servisu** - Zařízení v opravě
- **Vyřazeno** - Zařízení mimo provoz

## 🚀 Deployment

### Vercel (doporučeno)
1. Připojte repozitář k Vercel
2. Nastavte environment variables
3. Deploy automaticky při push do main

### Jiné platformy
Aplikace je kompatibilní s jakoukoliv platformou podporující Next.js:
- Netlify
- Railway
- DigitalOcean App Platform

## 📝 Licence

MIT License - viz LICENSE soubor

## 🤝 Přispívání

1. Fork repozitáře
2. Vytvořte feature branch
3. Commitněte změny
4. Vytvořte Pull Request

## 📞 Podpora

Pro podporu a dotazy kontaktujte vývojový tým nebo vytvořte issue v repozitáři.
