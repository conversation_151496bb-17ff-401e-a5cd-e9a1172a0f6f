-- Function to track device changes
CREATE OR REPLACE FUNCTION track_device_changes()
RETURNS TRIGGER AS $$
DECLARE
  changes JSONB := '{}';
  old_values JSONB := '{}';
  new_values JSONB := '{}';
BEGIN
  -- Track changes for UPDATE operations
  IF TG_OP = 'UPDATE' THEN
    -- Compare each field and track changes
    IF OLD.name != NEW.name THEN
      old_values := old_values || jsonb_build_object('name', OLD.name);
      new_values := new_values || jsonb_build_object('name', NEW.name);
    END IF;
    
    IF OLD.device_type IS DISTINCT FROM NEW.device_type THEN
      old_values := old_values || jsonb_build_object('device_type', OLD.device_type);
      new_values := new_values || jsonb_build_object('device_type', NEW.device_type);
    END IF;
    
    IF OLD.manufacturer IS DISTINCT FROM NEW.manufacturer THEN
      old_values := old_values || jsonb_build_object('manufacturer', OLD.manufacturer);
      new_values := new_values || jsonb_build_object('manufacturer', NEW.manufacturer);
    END IF;
    
    IF OLD.model IS DISTINCT FROM NEW.model THEN
      old_values := old_values || jsonb_build_object('model', OLD.model);
      new_values := new_values || jsonb_build_object('model', NEW.model);
    END IF;
    
    IF OLD.location IS DISTINCT FROM NEW.location THEN
      old_values := old_values || jsonb_build_object('location', OLD.location);
      new_values := new_values || jsonb_build_object('location', NEW.location);
    END IF;
    
    IF OLD.status != NEW.status THEN
      old_values := old_values || jsonb_build_object('status', OLD.status);
      new_values := new_values || jsonb_build_object('status', NEW.status);
    END IF;
    
    IF OLD.purchase_date IS DISTINCT FROM NEW.purchase_date THEN
      old_values := old_values || jsonb_build_object('purchase_date', OLD.purchase_date);
      new_values := new_values || jsonb_build_object('purchase_date', NEW.purchase_date);
    END IF;
    
    IF OLD.notes IS DISTINCT FROM NEW.notes THEN
      old_values := old_values || jsonb_build_object('notes', OLD.notes);
      new_values := new_values || jsonb_build_object('notes', NEW.notes);
    END IF;
    
    -- Only create history entry if there are actual changes
    IF old_values != '{}' THEN
      changes := jsonb_build_object(
        'operation', 'UPDATE',
        'old_values', old_values,
        'new_values', new_values
      );
      
      INSERT INTO device_history (device_id, changed_by, changes)
      VALUES (NEW.id, auth.uid(), changes);
    END IF;
    
    RETURN NEW;
  END IF;
  
  -- Track INSERT operations
  IF TG_OP = 'INSERT' THEN
    changes := jsonb_build_object(
      'operation', 'INSERT',
      'values', jsonb_build_object(
        'evidence_number', NEW.evidence_number,
        'name', NEW.name,
        'device_type', NEW.device_type,
        'manufacturer', NEW.manufacturer,
        'model', NEW.model,
        'location', NEW.location,
        'status', NEW.status,
        'purchase_date', NEW.purchase_date,
        'notes', NEW.notes
      )
    );
    
    INSERT INTO device_history (device_id, changed_by, changes)
    VALUES (NEW.id, auth.uid(), changes);
    
    RETURN NEW;
  END IF;
  
  -- Track DELETE operations
  IF TG_OP = 'DELETE' THEN
    changes := jsonb_build_object(
      'operation', 'DELETE',
      'values', jsonb_build_object(
        'evidence_number', OLD.evidence_number,
        'name', OLD.name,
        'device_type', OLD.device_type,
        'manufacturer', OLD.manufacturer,
        'model', OLD.model,
        'location', OLD.location,
        'status', OLD.status,
        'purchase_date', OLD.purchase_date,
        'notes', OLD.notes
      )
    );
    
    INSERT INTO device_history (device_id, changed_by, changes)
    VALUES (OLD.id, auth.uid(), changes);
    
    RETURN OLD;
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for device changes
CREATE TRIGGER track_device_changes_trigger
  AFTER INSERT OR UPDATE OR DELETE ON devices
  FOR EACH ROW
  EXECUTE FUNCTION track_device_changes();
