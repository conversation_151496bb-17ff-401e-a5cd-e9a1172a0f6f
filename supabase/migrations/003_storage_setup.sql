-- Create storage bucket for device files
INSERT INTO storage.buckets (id, name, public)
VALUES ('device-files', 'device-files', false);

-- Create storage policies
CREATE POLICY "Users can view files from their organization" ON storage.objects
FOR SELECT USING (
  bucket_id = 'device-files' AND
  EXISTS (
    SELECT 1 FROM device_files df
    JOIN devices d ON df.device_id = d.id
    JOIN organization_members om ON d.organization_id = om.organization_id
    WHERE df.file_path = name AND om.user_id = auth.uid()
  )
);

CREATE POLICY "Users can upload files to their organization devices" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'device-files' AND
  EXISTS (
    SELECT 1 FROM devices d
    JOIN organization_members om ON d.organization_id = om.organization_id
    WHERE om.user_id = auth.uid() AND om.role IN ('admin', 'technik')
  )
);

CREATE POLICY "Users can delete files from their organization devices" ON storage.objects
FOR DELETE USING (
  bucket_id = 'device-files' AND
  EXISTS (
    SELECT 1 FROM device_files df
    JOIN devices d ON df.device_id = d.id
    JOIN organization_members om ON d.organization_id = om.organization_id
    WHERE df.file_path = name AND om.user_id = auth.uid() AND om.role IN ('admin', 'technik')
  )
);
