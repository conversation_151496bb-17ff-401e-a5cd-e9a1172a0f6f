import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create superadmin user
  const superAdminPassword = await bcrypt.hash('SuperAdmin123!', 10)
  
  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      isSuperAdmin: true,
      password: superAdminPassword
    },
    create: {
      email: '<EMAIL>',
      password: superAdminPassword,
      name: 'Super Administrator',
      isSuperAdmin: true,
    },
  })

  console.log('✅ SuperAdmin user created:', superAdmin.email)

  // Update existing admin user to be superadmin as well
  await prisma.user.updateMany({
    where: { email: '<EMAIL>' },
    data: { isSuperAdmin: true }
  })

  console.log('✅ Updated <EMAIL> to superadmin')

  console.log('🎉 Seeding completed!')
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
