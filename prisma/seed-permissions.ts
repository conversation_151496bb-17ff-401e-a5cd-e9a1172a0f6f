import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding permissions and roles...')

  // Create permissions
  const permissions = [
    // Device permissions
    { name: 'devices.create', description: 'Vytvářet nová zařízení', resource: 'devices', action: 'create' },
    { name: 'devices.read', description: 'Zobrazovat zařízení', resource: 'devices', action: 'read' },
    { name: 'devices.update', description: 'Upravovat zařízení', resource: 'devices', action: 'update' },
    { name: 'devices.delete', description: 'Mazat zařízení', resource: 'devices', action: 'delete' },
    { name: 'devices.manage', description: 'Plná správa zařízení', resource: 'devices', action: 'manage' },
    
    // User permissions
    { name: 'users.create', description: 'Vytvářet nové uživatele', resource: 'users', action: 'create' },
    { name: 'users.read', description: 'Zobrazovat uživatele', resource: 'users', action: 'read' },
    { name: 'users.update', description: 'Upravovat uživatele', resource: 'users', action: 'update' },
    { name: 'users.delete', description: 'Mazat uživatele', resource: 'users', action: 'delete' },
    { name: 'users.manage', description: 'Plná správa uživatelů', resource: 'users', action: 'manage' },
    
    // Organization permissions
    { name: 'organizations.create', description: 'Vytvářet organizace', resource: 'organizations', action: 'create' },
    { name: 'organizations.read', description: 'Zobrazovat organizace', resource: 'organizations', action: 'read' },
    { name: 'organizations.update', description: 'Upravovat organizace', resource: 'organizations', action: 'update' },
    { name: 'organizations.delete', description: 'Mazat organizace', resource: 'organizations', action: 'delete' },
    { name: 'organizations.manage', description: 'Plná správa organizací', resource: 'organizations', action: 'manage' },
    
    // Team permissions
    { name: 'teams.create', description: 'Vytvářet týmy', resource: 'teams', action: 'create' },
    { name: 'teams.read', description: 'Zobrazovat týmy', resource: 'teams', action: 'read' },
    { name: 'teams.update', description: 'Upravovat týmy', resource: 'teams', action: 'update' },
    { name: 'teams.delete', description: 'Mazat týmy', resource: 'teams', action: 'delete' },
    { name: 'teams.manage', description: 'Plná správa týmů', resource: 'teams', action: 'manage' },
    
    // Department permissions
    { name: 'departments.create', description: 'Vytvářet oddělení', resource: 'departments', action: 'create' },
    { name: 'departments.read', description: 'Zobrazovat oddělení', resource: 'departments', action: 'read' },
    { name: 'departments.update', description: 'Upravovat oddělení', resource: 'departments', action: 'update' },
    { name: 'departments.delete', description: 'Mazat oddělení', resource: 'departments', action: 'delete' },
    { name: 'departments.manage', description: 'Plná správa oddělení', resource: 'departments', action: 'manage' },
    
    // Audit permissions
    { name: 'audit.read', description: 'Zobrazovat audit log', resource: 'audit', action: 'read' },
    { name: 'audit.manage', description: 'Plná správa audit logu', resource: 'audit', action: 'manage' },
    
    // Reports permissions
    { name: 'reports.read', description: 'Zobrazovat reporty', resource: 'reports', action: 'read' },
    { name: 'reports.create', description: 'Vytvářet reporty', resource: 'reports', action: 'create' },
    { name: 'reports.manage', description: 'Plná správa reportů', resource: 'reports', action: 'manage' },
    
    // Settings permissions
    { name: 'settings.read', description: 'Zobrazovat nastavení', resource: 'settings', action: 'read' },
    { name: 'settings.update', description: 'Upravovat nastavení', resource: 'settings', action: 'update' },
    { name: 'settings.manage', description: 'Plná správa nastavení', resource: 'settings', action: 'manage' },
  ]

  for (const permission of permissions) {
    await prisma.permission.upsert({
      where: { name: permission.name },
      update: {},
      create: permission,
    })
  }

  console.log('✅ Permissions created')

  // Create system roles
  const roles = [
    {
      name: 'superadmin',
      description: 'Superadministrátor s plnými oprávněními',
      isSystem: true,
      permissions: permissions.map(p => p.name), // All permissions
    },
    {
      name: 'admin',
      description: 'Administrátor organizace',
      isSystem: true,
      permissions: [
        'devices.manage', 'users.manage', 'teams.manage', 'departments.manage',
        'audit.read', 'reports.manage', 'settings.update'
      ],
    },
    {
      name: 'device_manager',
      description: 'Správce zařízení',
      isSystem: true,
      permissions: [
        'devices.manage', 'teams.read', 'departments.read', 'reports.read'
      ],
    },
    {
      name: 'technician',
      description: 'Technik',
      isSystem: true,
      permissions: [
        'devices.create', 'devices.read', 'devices.update', 'teams.read', 'departments.read'
      ],
    },
    {
      name: 'viewer',
      description: 'Pouze čtení',
      isSystem: true,
      permissions: [
        'devices.read', 'teams.read', 'departments.read', 'reports.read'
      ],
    },
  ]

  for (const roleData of roles) {
    const role = await prisma.role.upsert({
      where: { name: roleData.name },
      update: {},
      create: {
        name: roleData.name,
        description: roleData.description,
        isSystem: roleData.isSystem,
      },
    })

    // Assign permissions to role
    for (const permissionName of roleData.permissions) {
      const permission = await prisma.permission.findUnique({
        where: { name: permissionName },
      })

      if (permission) {
        await prisma.rolePermission.upsert({
          where: {
            roleId_permissionId: {
              roleId: role.id,
              permissionId: permission.id,
            },
          },
          update: {},
          create: {
            roleId: role.id,
            permissionId: permission.id,
          },
        })
      }
    }
  }

  console.log('✅ Roles created')

  // Create test organization
  const organization = await prisma.organization.upsert({
    where: { slug: 'test-org' },
    update: {},
    create: {
      name: 'Test Organization',
      slug: 'test-org',
      description: 'Testovací organizace',
    },
  })

  // Create test superadmin user
  const hashedPassword = await bcrypt.hash('admin123', 10)
  const superadminRole = await prisma.role.findUnique({ where: { name: 'superadmin' } })
  
  const superadmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {
      password: hashedPassword,
      name: 'Super Admin',
      isSuperAdmin: true,
    },
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Super Admin',
      isSuperAdmin: true,
    },
  })

  // Create organization membership for superadmin
  if (superadminRole) {
    await prisma.organizationMember.upsert({
      where: {
        userId_organizationId: {
          userId: superadmin.id,
          organizationId: organization.id,
        },
      },
      update: {},
      create: {
        userId: superadmin.id,
        organizationId: organization.id,
        roleId: superadminRole.id,
        legacyRole: 'admin',
      },
    })
  }

  console.log('✅ Test data created')
  console.log('🎉 Seeding completed!')
  console.log('📧 Login: <EMAIL>')
  console.log('🔑 Password: admin123')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
