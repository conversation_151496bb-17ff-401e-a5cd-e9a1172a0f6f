// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  password    String
  name        String?
  isSuperAd<PERSON> @default(false)
  lastLogin   DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  organizationMembers OrganizationMember[]
  sessions            Session[]
  userPermissions     UserPermission[]
  teamMembers         TeamMember[]
  auditLogs           AuditLog[]

  @@map("users")
}

model Organization {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  members     OrganizationMember[]
  devices     Device[]
  departments Department[]
  teams       Team[]

  @@map("organizations")
}

model OrganizationMember {
  id             String @id @default(cuid())
  userId         String
  organizationId String
  roleId         String? // Reference to Role model
  legacyRole     String? // Keep for backward compatibility: 'admin', 'technik', 'ctenar'
  createdAt      DateTime @default(now())

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  role         Role?        @relation(fields: [roleId], references: [id], onDelete: SetNull)

  @@unique([userId, organizationId])
  @@map("organization_members")
}

model Device {
  id              String   @id @default(cuid())
  evidenceNumber  String   @unique
  name            String
  description     String?
  deviceType      String?
  manufacturer    String?
  model           String?
  serialNumber    String?
  location        String?
  status          String   @default("aktivni") // 'aktivni', 'zapujceno', 'servis', 'vyrazeno'
  purchaseDate    DateTime?
  warrantyExpiry  DateTime?
  notes           String?
  organizationId  String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  organization Organization   @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  files        DeviceFile[]
  history      DeviceHistory[]

  @@map("devices")
}

model DeviceFile {
  id       String @id @default(cuid())
  deviceId String
  filename String
  originalName String
  mimeType String
  size     Int
  path     String
  createdAt DateTime @default(now())

  // Relations
  device Device @relation(fields: [deviceId], references: [id], onDelete: Cascade)

  @@map("device_files")
}

model DeviceHistory {
  id        String   @id @default(cuid())
  deviceId  String
  action    String   // 'INSERT', 'UPDATE', 'DELETE'
  oldValues String?  // JSON string
  newValues String?  // JSON string
  changedBy String?
  createdAt DateTime @default(now())

  // Relations
  device Device @relation(fields: [deviceId], references: [id], onDelete: Cascade)

  @@map("device_history")
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Permission system models
model Permission {
  id          String @id @default(cuid())
  name        String @unique // e.g., 'devices.create', 'devices.read', 'devices.update', 'devices.delete'
  description String?
  resource    String // e.g., 'devices', 'users', 'organizations'
  action      String // e.g., 'create', 'read', 'update', 'delete', 'manage'
  createdAt   DateTime @default(now())

  // Relations
  rolePermissions RolePermission[]
  userPermissions UserPermission[]

  @@map("permissions")
}

model Role {
  id          String @id @default(cuid())
  name        String @unique // e.g., 'device_manager', 'user_admin', 'viewer'
  description String?
  isSystem    Boolean @default(false) // System roles cannot be deleted
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  rolePermissions     RolePermission[]
  organizationMembers OrganizationMember[]

  @@map("roles")
}

model RolePermission {
  id           String @id @default(cuid())
  roleId       String
  permissionId String
  createdAt    DateTime @default(now())

  // Relations
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

model UserPermission {
  id           String @id @default(cuid())
  userId       String
  permissionId String
  granted      Boolean @default(true) // true = granted, false = denied (overrides role permissions)
  createdAt    DateTime @default(now())

  // Relations
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([userId, permissionId])
  @@map("user_permissions")
}

// Department and Team models
model Department {
  id             String @id @default(cuid())
  name           String
  description    String?
  organizationId String
  managerId      String? // User who manages this department
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  teams        Team[]

  @@unique([name, organizationId])
  @@map("departments")
}

model Team {
  id           String @id @default(cuid())
  name         String
  description  String?
  departmentId String?
  organizationId String
  leaderId     String? // User who leads this team
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  department   Department?  @relation(fields: [departmentId], references: [id], onDelete: SetNull)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  members      TeamMember[]

  @@unique([name, organizationId])
  @@map("teams")
}

model TeamMember {
  id       String @id @default(cuid())
  teamId   String
  userId   String
  role     String? // e.g., 'member', 'lead', 'coordinator'
  joinedAt DateTime @default(now())

  // Relations
  team Team @relation(fields: [teamId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([teamId, userId])
  @@map("team_members")
}

// Audit Log model
model AuditLog {
  id           String @id @default(cuid())
  userId       String?
  action       String   // e.g., 'CREATE', 'UPDATE', 'DELETE', 'LOGIN', 'LOGOUT'
  resource     String   // e.g., 'device', 'user', 'organization'
  resourceId   String?  // ID of the affected resource
  details      String?  // JSON string with additional details
  ipAddress    String?
  userAgent    String?
  organizationId String?
  createdAt    DateTime @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([resource, resourceId])
  @@index([createdAt])
  @@map("audit_logs")
}
