# 📱 Mobilní responsivita - RokitBox

## ✅ Implementované funkce

### 1. 🎯 Touch-friendly ovládání
- **Minimální velikost touch targetů**: 44px (Apple HIG standard)
- **Touch manipulation**: Optimalizace pro touch zařízení
- **Active states**: Vizuální feedback při dotyku (`active:scale-95`)
- **Hover states**: Zachovány pro desktop, deaktivovány na touch zařízeních

### 2. 📱 Mobilní navigace
- **Hamburger menu**: Vylepšené s větším touch targety
- **Mobile header**: Sticky pozice s optimalizovaným layoutem
- **Bottom navigation**: Nová komponenta pro rychlý přístup k hlavním sekcím
- **Floating Action Button (FAB)**: Pro rychlé akce na mobilech

### 3. 🎨 Responsive design
- **Mobile-first approach**: Základní styly pro mobily, rozšíření pro desktop
- **Breakpoints**:
  - `xs`: 475px (malé telefony)
  - `sm`: 640px (velké telefony)
  - `tablet`: 768px (tablety)
  - `md`: 768px (malé tablety)
  - `lg`: 1024px (desktop)
  - `desktop`: 1024px (velké desktopy)

### 4. 🔄 Pull-to-refresh
- **Nativní gesto**: Táhnutí dolů pro obnovení obsahu
- **Vizuální feedback**: Animovaný indikátor s progress barem
- **Threshold**: 80px pro aktivaci
- **Smooth animations**: Plynulé přechody a animace

### 5. 👆 Swipe actions
- **Swipe gestures**: Přejetí prstem pro rychlé akce
- **Levé akce**: Editace (zelená)
- **Pravé akce**: Mazání (červená)
- **Visual feedback**: Barevné pozadí s ikonami
- **Threshold**: 80px pro aktivaci

## 🛠️ Nové komponenty

### FloatingActionButton
```typescript
// Základní použití
<FloatingActionButton 
  onClick={() => router.push('/dashboard/devices/new')}
  icon={<Plus className="h-6 w-6" />}
/>

// S více akcemi
<FloatingActionButton 
  actions={[
    { icon: <Plus />, label: 'Přidat', onClick: () => {} },
    { icon: <Search />, label: 'Hledat', onClick: () => {} }
  ]}
/>
```

### BottomNavigation
```typescript
<BottomNavigation 
  items={[
    { name: 'Dashboard', href: '/dashboard', icon: LayoutDashboard },
    { name: 'Zařízení', href: '/dashboard/devices', icon: Monitor }
  ]}
/>
```

### PullToRefresh
```typescript
<PullToRefresh onRefresh={handleRefresh}>
  <div>Obsah stránky</div>
</PullToRefresh>
```

### SwipeActions
```typescript
<SwipeActions 
  actions={[
    { icon: <Edit />, label: 'Upravit', onClick: () => {}, side: 'left' },
    { icon: <Trash />, label: 'Smazat', onClick: () => {}, side: 'right' }
  ]}
>
  <Card>Obsah karty</Card>
</SwipeActions>
```

## 📐 Layout optimalizace

### Dashboard
- **Responsive grid**: `grid-cols-1 sm:grid-cols-2 lg:grid-cols-4`
- **Flexible buttons**: Plná šířka na mobilu, auto na desktopu
- **Optimalizované spacing**: Menší mezery na mobilech

### Device list
- **Card layout**: Optimalizované pro touch
- **Hidden actions**: Desktop akce skryté na mobilech (nahrazeny swipe)
- **Responsive typography**: Menší text na mobilech

### Forms
- **Stacked layout**: Vertikální layout na mobilech
- **Full-width inputs**: Plná šířka pro lepší použitelnost
- **Touch-optimized selects**: Větší touch targety

## 🎯 Touch optimalizace

### Button komponenta
```typescript
// Nová velikost pro mobily
size: {
  touch: "h-12 px-6 py-3 min-h-[48px] text-base"
}

// Automatické touch třídy
className="touch-manipulation active:scale-95"
```

### Input komponenta
```typescript
// Minimální výška a touch optimalizace
className="min-h-[44px] touch-manipulation text-base sm:text-sm"
```

## 📱 Mobilní UX vylepšení

### 1. Sticky header
- Header zůstává viditelný při scrollování
- Optimalizovaná výška pro mobily

### 2. Bottom spacing
- Automatické odsazení pro bottom navigation
- Zabránění překrytí obsahu

### 3. Responsive typography
- Větší text na mobilech pro lepší čitelnost
- Škálování podle velikosti obrazovky

### 4. Loading states
- Optimalizované loading spinnery
- Touch-friendly loading indikátory

## 🔧 Technické detaily

### CSS třídy
```css
/* Touch optimalizace */
.touch-manipulation { touch-action: manipulation; }

/* Active states */
.active\:scale-95:active { transform: scale(0.95); }

/* Responsive text */
.text-base { font-size: 1rem; }
.sm\:text-sm { font-size: 0.875rem; }

/* Minimum touch targets */
.min-h-\[44px\] { min-height: 44px; }
.min-w-\[44px\] { min-width: 44px; }
```

### Viewport konfigurace
```typescript
export function generateViewport() {
  return {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
    themeColor: "#ff6600",
  }
}
```

## 📊 Testování

### Doporučené rozlišení pro testování
- **iPhone SE**: 375x667px
- **iPhone 12**: 390x844px
- **iPad**: 768x1024px
- **iPad Pro**: 1024x1366px

### Chrome DevTools
1. Otevřít DevTools (F12)
2. Kliknout na ikonu telefonu
3. Vybrat zařízení nebo nastavit custom rozlišení
4. Testovat touch gesta a responsivitu

## 🚀 Další vylepšení

### Plánované funkce
- [ ] Offline podpora (PWA)
- [ ] Push notifikace
- [ ] Biometrické přihlášení
- [ ] Dark mode pro mobily
- [ ] Haptic feedback
- [ ] Voice search

### Performance optimalizace
- [ ] Lazy loading obrázků
- [ ] Virtual scrolling pro dlouhé seznamy
- [ ] Service worker caching
- [ ] Bundle splitting pro mobily
