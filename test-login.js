// Test script pro ov<PERSON><PERSON><PERSON><PERSON>
const testLogin = async () => {
  try {
    console.log('Testing login...')
    
    const response = await fetch('http://localhost:3007/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'admin123'
      }),
    })

    console.log('Response status:', response.status)
    console.log('Response headers:', Object.fromEntries(response.headers.entries()))
    
    const data = await response.json()
    console.log('Response data:', data)
    
    if (response.ok) {
      console.log('✅ Login successful!')
      
      // Test session
      const sessionCookie = response.headers.get('set-cookie')
      console.log('Session cookie:', sessionCookie)
      
      // Test dashboard access
      const dashboardResponse = await fetch('http://localhost:3007/api/auth/me', {
        headers: {
          'Cookie': sessionCookie || ''
        }
      })
      
      console.log('Dashboard access status:', dashboardResponse.status)
      const dashboardData = await dashboardResponse.json()
      console.log('Dashboard data:', dashboardData)
      
    } else {
      console.log('❌ Login failed:', data.error)
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

testLogin()
